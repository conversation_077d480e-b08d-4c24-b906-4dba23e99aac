/**
 * Search Bar Component JavaScript
 * Handles global search functionality with keyboard shortcuts and real-time results
 */

class SearchBarComponent {
    constructor(options = {}) {
        this.options = {
            searchData: options.searchData || window.searchData || [],
            maxResults: options.maxResults || 10,
            minSearchLength: options.minSearchLength || 1,
            debounceDelay: options.debounceDelay || 300,
            enableKeyboardShortcuts: options.enableKeyboardShortcuts !== false,
            ...options
        };

        this.searchInput = null;
        this.searchResults = null;
        this.searchOverlay = null;
        this.currentResults = [];
        this.selectedIndex = -1;
        this.recentSearches = this.loadRecentSearches();
        this.searchTimeout = null;
        this.isOpen = false;

        this.init();
    }

    init() {
        this.bindElements();
        this.bindEvents();
        this.populateRecentSearches();
        console.log('Search Bar Component initialized');
    }

    bindElements() {
        this.searchInput = document.getElementById('search-input');
        this.searchResults = document.getElementById('search-results');
        this.searchOverlay = document.getElementById('search-overlay');
        this.searchResultsClose = document.getElementById('search-results-close');
        this.searchItems = document.getElementById('search-items');
        this.recentSearchesContainer = document.getElementById('recent-searches');
        this.clearRecentBtn = document.getElementById('clear-recent-searches');
        this.noResultsContainer = document.getElementById('search-no-results');
        this.resultsCount = document.querySelector('.results-count');
        this.searchTerm = document.querySelector('.search-term');
    }

    bindEvents() {
        if (!this.searchInput) return;

        // Search input events
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });

        this.searchInput.addEventListener('focus', () => {
            this.openSearch();
        });

        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeyNavigation(e);
        });

        // Close button
        if (this.searchResultsClose) {
            this.searchResultsClose.addEventListener('click', () => {
                this.closeSearch();
            });
        }

        // Overlay click
        if (this.searchOverlay) {
            this.searchOverlay.addEventListener('click', () => {
                this.closeSearch();
            });
        }

        // Clear recent searches
        if (this.clearRecentBtn) {
            this.clearRecentBtn.addEventListener('click', () => {
                this.clearRecentSearches();
            });
        }

        // Suggestion tags
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('suggestion-tag')) {
                const suggestion = e.target.getAttribute('data-suggestion');
                this.searchInput.value = suggestion;
                this.handleSearchInput(suggestion);
            }
        });

        // Keyboard shortcuts
        if (this.options.enableKeyboardShortcuts) {
            document.addEventListener('keydown', (e) => {
                // Ctrl+K or Cmd+K to focus search
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    this.focusSearch();
                }

                // Escape to close
                if (e.key === 'Escape' && this.isOpen) {
                    this.closeSearch();
                }
            });
        }
    }

    handleSearchInput(value) {
        clearTimeout(this.searchTimeout);
        
        this.searchTimeout = setTimeout(() => {
            this.performSearch(value);
        }, this.options.debounceDelay);
    }

    performSearch(query) {
        const trimmedQuery = query.trim();
        
        if (trimmedQuery.length < this.options.minSearchLength) {
            this.showRecentSearches();
            return;
        }

        const results = this.searchData(trimmedQuery);
        this.displayResults(results, trimmedQuery);
        
        // Add to recent searches if not empty
        if (trimmedQuery && results.length > 0) {
            this.addToRecentSearches(trimmedQuery);
        }
    }

    searchData(query) {
        const searchTerm = query.toLowerCase();
        const results = [];

        this.options.searchData.forEach(item => {
            let score = 0;
            const name = item.name.toLowerCase();
            const description = (item.description || '').toLowerCase();
            const category = (item.category || '').toLowerCase();

            // Exact match in name (highest priority)
            if (name === searchTerm) {
                score = 100;
            }
            // Name starts with search term
            else if (name.startsWith(searchTerm)) {
                score = 90;
            }
            // Name contains search term
            else if (name.includes(searchTerm)) {
                score = 80;
            }
            // Description contains search term
            else if (description.includes(searchTerm)) {
                score = 60;
            }
            // Category contains search term
            else if (category.includes(searchTerm)) {
                score = 40;
            }

            if (score > 0) {
                results.push({ ...item, score });
            }
        });

        // Sort by score (descending) and limit results
        return results
            .sort((a, b) => b.score - a.score)
            .slice(0, this.options.maxResults);
    }

    displayResults(results, query) {
        this.currentResults = results;
        this.selectedIndex = -1;

        // Update header
        if (this.resultsCount) {
            this.resultsCount.textContent = `${results.length} result${results.length !== 1 ? 's' : ''}`;
        }
        if (this.searchTerm) {
            this.searchTerm.textContent = query ? `for "${query}"` : '';
        }

        // Clear previous results
        if (this.searchItems) {
            this.searchItems.innerHTML = '';
        }

        // Hide recent searches section
        const recentSection = document.getElementById('recent-searches-section');
        if (recentSection) {
            recentSection.style.display = 'none';
        }

        // Show/hide no results
        if (this.noResultsContainer) {
            this.noResultsContainer.style.display = results.length === 0 ? 'block' : 'none';
        }

        // Show results section
        const resultsSection = document.getElementById('search-results-section');
        if (resultsSection) {
            resultsSection.style.display = results.length > 0 ? 'block' : 'none';
        }

        // Populate results
        results.forEach((item, index) => {
            const resultElement = this.createResultElement(item, index);
            if (this.searchItems) {
                this.searchItems.appendChild(resultElement);
            }
        });

        this.openSearch();
    }

    createResultElement(item, index) {
        const element = document.createElement('button');
        element.className = 'search-item';
        element.setAttribute('data-index', index);
        
        element.innerHTML = `
            <div class="search-item-icon">
                <i class="${item.icon || 'fas fa-file'}"></i>
            </div>
            <div class="search-item-content">
                <div class="search-item-title">${this.highlightText(item.name, this.searchInput.value)}</div>
                <div class="search-item-description">${item.description || ''}</div>
            </div>
            <div class="search-item-meta">
                <div class="search-item-category">${item.category || item.type || ''}</div>
            </div>
        `;

        element.addEventListener('click', () => {
            this.selectResult(item);
        });

        return element;
    }

    highlightText(text, query) {
        if (!query) return text;
        
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    showRecentSearches() {
        // Clear search results
        if (this.searchItems) {
            this.searchItems.innerHTML = '';
        }

        // Update header
        if (this.resultsCount) {
            this.resultsCount.textContent = '';
        }
        if (this.searchTerm) {
            this.searchTerm.textContent = '';
        }

        // Show recent searches section
        const recentSection = document.getElementById('recent-searches-section');
        if (recentSection) {
            recentSection.style.display = this.recentSearches.length > 0 ? 'block' : 'none';
        }

        // Hide results section and no results
        const resultsSection = document.getElementById('search-results-section');
        if (resultsSection) {
            resultsSection.style.display = 'none';
        }
        if (this.noResultsContainer) {
            this.noResultsContainer.style.display = 'none';
        }

        this.openSearch();
    }

    populateRecentSearches() {
        if (!this.recentSearchesContainer) return;

        this.recentSearchesContainer.innerHTML = '';

        this.recentSearches.forEach(search => {
            const element = document.createElement('button');
            element.className = 'search-item';
            element.innerHTML = `
                <div class="search-item-icon">
                    <i class="fas fa-history"></i>
                </div>
                <div class="search-item-content">
                    <div class="search-item-title">${search}</div>
                    <div class="search-item-description">Recent search</div>
                </div>
            `;

            element.addEventListener('click', () => {
                this.searchInput.value = search;
                this.handleSearchInput(search);
            });

            this.recentSearchesContainer.appendChild(element);
        });
    }

    handleKeyNavigation(e) {
        if (!this.isOpen) return;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.navigateResults(1);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.navigateResults(-1);
                break;
            case 'Enter':
                e.preventDefault();
                this.selectCurrentResult();
                break;
            case 'Escape':
                this.closeSearch();
                break;
        }
    }

    navigateResults(direction) {
        const items = this.searchItems?.querySelectorAll('.search-item') || [];
        if (items.length === 0) return;

        // Remove previous selection
        items.forEach(item => item.classList.remove('selected'));

        // Update selected index
        this.selectedIndex += direction;
        
        if (this.selectedIndex < 0) {
            this.selectedIndex = items.length - 1;
        } else if (this.selectedIndex >= items.length) {
            this.selectedIndex = 0;
        }

        // Add selection to current item
        items[this.selectedIndex]?.classList.add('selected');
        items[this.selectedIndex]?.scrollIntoView({ block: 'nearest' });
    }

    selectCurrentResult() {
        if (this.selectedIndex >= 0 && this.currentResults[this.selectedIndex]) {
            this.selectResult(this.currentResults[this.selectedIndex]);
        }
    }

    selectResult(item) {
        console.log('Selected result:', item);
        
        // Dispatch custom event
        const event = new CustomEvent('searchResultSelected', {
            detail: { item }
        });
        document.dispatchEvent(event);

        // Default navigation (can be overridden by event listener)
        if (item.href) {
            window.location.href = item.href;
        }

        this.closeSearch();
    }

    openSearch() {
        if (this.isOpen) return;

        this.isOpen = true;
        
        if (this.searchResults) {
            this.searchResults.classList.add('active');
        }
        if (this.searchOverlay) {
            this.searchOverlay.classList.add('active');
        }

        document.body.style.overflow = 'hidden';
    }

    closeSearch() {
        this.isOpen = false;
        
        if (this.searchResults) {
            this.searchResults.classList.remove('active');
        }
        if (this.searchOverlay) {
            this.searchOverlay.classList.remove('active');
        }

        document.body.style.overflow = '';
        this.selectedIndex = -1;
    }

    focusSearch() {
        if (this.searchInput) {
            this.searchInput.focus();
            this.searchInput.select();
        }
    }

    addToRecentSearches(query) {
        // Remove if already exists
        this.recentSearches = this.recentSearches.filter(search => search !== query);
        
        // Add to beginning
        this.recentSearches.unshift(query);
        
        // Limit to 5 recent searches
        this.recentSearches = this.recentSearches.slice(0, 5);
        
        // Save to localStorage
        this.saveRecentSearches();
        this.populateRecentSearches();
    }

    clearRecentSearches() {
        this.recentSearches = [];
        this.saveRecentSearches();
        this.populateRecentSearches();
        
        const recentSection = document.getElementById('recent-searches-section');
        if (recentSection) {
            recentSection.style.display = 'none';
        }
    }

    loadRecentSearches() {
        try {
            return JSON.parse(localStorage.getItem('search-recent-searches') || '[]');
        } catch {
            return [];
        }
    }

    saveRecentSearches() {
        try {
            localStorage.setItem('search-recent-searches', JSON.stringify(this.recentSearches));
        } catch (e) {
            console.warn('Could not save recent searches:', e);
        }
    }

    // Public API methods
    updateSearchData(newData) {
        this.options.searchData = newData;
    }

    search(query) {
        this.searchInput.value = query;
        this.handleSearchInput(query);
    }

    clear() {
        this.searchInput.value = '';
        this.showRecentSearches();
    }
}

// Initialize the component when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.searchBarComponent = new SearchBarComponent();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchBarComponent;
}

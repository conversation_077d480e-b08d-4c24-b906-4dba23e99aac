# Search Bar Component

A powerful, responsive search component with real-time results, keyboard navigation, and recent search history.

## Features

- **Real-time Search**: Instant search results as you type
- **Keyboard Shortcuts**: Ctrl+K to focus, arrow keys for navigation
- **Recent Searches**: Automatically saves and displays recent searches
- **Smart Scoring**: Intelligent result ranking based on relevance
- **Responsive Design**: Works on all screen sizes
- **Customizable**: Easy to configure and style
- **Accessibility**: Full keyboard navigation and ARIA support
- **Local Storage**: Persists recent searches across sessions

## Files

- `search-bar.html` - Complete HTML structure with sample data
- `search-bar.css` - All styling including responsive design
- `search-bar.js` - JavaScript functionality and search logic
- `README.md` - This documentation file

## Dependencies

- **Font Awesome 6.4.0+** - For icons
- **Modern Browser** - Supports ES6+, localStorage, and CSS Grid

## Quick Start

### 1. Include Required Files

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Page</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Search Bar Component CSS -->
    <link rel="stylesheet" href="path/to/search-bar.css">
</head>
<body>
    <!-- Your search bar HTML here -->
    
    <!-- Search Bar Component JavaScript -->
    <script src="path/to/search-bar.js"></script>
</body>
</html>
```

### 2. Basic HTML Structure

```html
<div class="search-bar-component">
    <header class="search-header">
        <div class="header-content">
            <div class="header-center">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="search-input" class="search-input" 
                           placeholder="Search..." />
                </div>
            </div>
        </div>
    </header>
    
    <div class="search-results" id="search-results">
        <!-- Search results will be populated here -->
    </div>
    
    <div class="search-overlay" id="search-overlay"></div>
</div>
```

### 3. Provide Search Data

```javascript
// Define your search data
window.searchData = [
    {
        id: 'unique-id',
        name: 'Item Name',
        type: 'page',
        icon: 'fas fa-file',
        href: '/path/to/item',
        category: 'Category',
        description: 'Item description'
    },
    // ... more items
];
```

## Configuration

### Initialize with Custom Options

```javascript
const searchBar = new SearchBarComponent({
    searchData: yourSearchData,
    maxResults: 15,
    minSearchLength: 2,
    debounceDelay: 200,
    enableKeyboardShortcuts: true
});
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `searchData` | Array | `[]` | Array of searchable items |
| `maxResults` | Number | `10` | Maximum number of results to show |
| `minSearchLength` | Number | `1` | Minimum characters before searching |
| `debounceDelay` | Number | `300` | Delay in ms before searching |
| `enableKeyboardShortcuts` | Boolean | `true` | Enable Ctrl+K shortcut |

## Search Data Format

Each search item should have the following structure:

```javascript
{
    id: 'unique-identifier',        // Required: Unique ID
    name: 'Display Name',           // Required: Item name
    type: 'page|feature|action',    // Optional: Item type
    icon: 'fas fa-icon-name',       // Optional: Font Awesome icon
    href: '/path/to/item',          // Optional: Navigation URL
    category: 'Category Name',      // Optional: Category for grouping
    description: 'Item description' // Optional: Description text
}
```

## JavaScript API

### Accessing the Component

```javascript
// Component is available globally after initialization
const searchBar = window.searchBarComponent;
```

### Methods

```javascript
// Update search data
searchBar.updateSearchData(newSearchData);

// Programmatically search
searchBar.search('query');

// Clear search
searchBar.clear();

// Focus search input
searchBar.focusSearch();

// Open/close search
searchBar.openSearch();
searchBar.closeSearch();
```

### Events

Listen for search result selections:

```javascript
document.addEventListener('searchResultSelected', (event) => {
    const selectedItem = event.detail.item;
    console.log('User selected:', selectedItem);
    
    // Custom navigation logic
    if (selectedItem.type === 'action') {
        // Handle action items
        performAction(selectedItem.id);
    } else {
        // Handle navigation
        window.location.href = selectedItem.href;
    }
});
```

## Keyboard Shortcuts

- **Ctrl+K** (or Cmd+K): Focus search input
- **↑/↓ Arrow Keys**: Navigate through results
- **Enter**: Select highlighted result
- **Escape**: Close search results

## Customization

### Styling

Override CSS custom properties:

```css
:root {
    --primary-color: #your-primary-color;
    --accent-color: #your-accent-color;
    --header-background: #your-header-bg;
    --input-background: #your-input-bg;
    /* ... other properties */
}
```

### Custom Result Template

Override the `createResultElement` method:

```javascript
class CustomSearchBar extends SearchBarComponent {
    createResultElement(item, index) {
        const element = document.createElement('button');
        element.className = 'search-item';
        
        // Your custom template
        element.innerHTML = `
            <div class="custom-result">
                <h4>${item.name}</h4>
                <p>${item.description}</p>
            </div>
        `;
        
        element.addEventListener('click', () => {
            this.selectResult(item);
        });
        
        return element;
    }
}
```

### Search Algorithm

Override the `searchData` method for custom search logic:

```javascript
class CustomSearchBar extends SearchBarComponent {
    searchData(query) {
        // Your custom search algorithm
        return this.options.searchData.filter(item => {
            return item.name.toLowerCase().includes(query.toLowerCase());
        });
    }
}
```

## Integration Examples

### With React

```jsx
import { useEffect, useRef } from 'react';

function SearchComponent({ searchData }) {
    const searchRef = useRef();
    
    useEffect(() => {
        const searchBar = new SearchBarComponent({
            searchData: searchData
        });
        
        return () => {
            // Cleanup if needed
        };
    }, [searchData]);
    
    return (
        <div ref={searchRef}>
            {/* Search bar HTML */}
        </div>
    );
}
```

### With Vue

```vue
<template>
    <div class="search-bar-component">
        <!-- Search bar HTML -->
    </div>
</template>

<script>
export default {
    props: ['searchData'],
    mounted() {
        this.searchBar = new SearchBarComponent({
            searchData: this.searchData
        });
    },
    watch: {
        searchData(newData) {
            this.searchBar.updateSearchData(newData);
        }
    }
}
</script>
```

## Browser Support

- **Chrome**: 60+
- **Firefox**: 60+
- **Safari**: 12+
- **Edge**: 79+

## Performance Tips

1. **Limit Search Data**: Keep search data under 1000 items for best performance
2. **Use Debouncing**: Default 300ms debounce prevents excessive searches
3. **Lazy Loading**: Load search data asynchronously if large
4. **Virtual Scrolling**: Consider for very large result sets

## Accessibility Features

- ARIA labels and roles
- Keyboard navigation
- Focus management
- Screen reader support
- High contrast support

## License

This component is provided as-is for educational and development purposes.

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prevost Login</title>

    <!-- Material-UI CSS -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mui/material@5.14.20/umd/material-ui.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="root">
        <div class="login-container">
            <!-- Background overlay -->
            <div class="background-overlay"></div>

            <!-- Main content -->
            <div class="content-wrapper">
                <!-- Login form -->
                <div class="login-form-container">
                    <div class="login-card">
                        <!-- Logo inside card -->
                        <div class="logo-section">
                            <img src="Prevost.png" alt="Provost Logo" class="logo">
                        </div>

                        <h1 class="login-title">Welcome</h1>
                        <p class="login-subtitle">Please Enter Login Information</p>

                        <form id="loginForm" class="login-form" novalidate>
                            <!-- Login ID Field -->
                            <div class="form-field">
                                <div class="mui-textfield">
                                    <input
                                        type="text"
                                        id="loginId"
                                        name="loginId"
                                        required
                                        aria-label="Login ID"
                                        class="mui-input"
                                    >
                                    <label for="loginId" class="mui-label">Login ID</label>
                                    <div class="mui-underline"></div>
                                </div>
                                <div class="error-message" id="loginIdError"></div>
                            </div>

                            <!-- Password Field -->
                            <div class="form-field">
                                <div class="mui-textfield password-field">
                                    <input
                                        type="password"
                                        id="password"
                                        name="password"
                                        required
                                        aria-label="Password"
                                        class="mui-input"
                                    >
                                    <label for="password" class="mui-label">Password</label>
                                    <button
                                        type="button"
                                        class="password-toggle"
                                        id="passwordToggle"
                                        aria-label="Toggle password visibility"
                                    >
                                        <span class="material-icons">visibility_off</span>
                                    </button>
                                    <div class="mui-underline"></div>
                                </div>
                                <div class="error-message" id="passwordError"></div>
                            </div>

                            <!-- Branch Selection -->
                            <div class="form-field">
                                <div class="mui-select-container">
                                    <div class="mui-select-wrapper">
                                        <input
                                            type="text"
                                            id="branchSearch"
                                            name="branchSearch"
                                            class="mui-select-search"
                                            placeholder=""
                                            autocomplete="off"
                                            disabled
                                        >
                                        <input
                                            type="hidden"
                                            id="branch"
                                            name="branch"
                                            required
                                        >
                                        <label for="branchSearch" class="mui-select-label">Branch</label>
                                        <div class="mui-select-arrow">
                                            <span class="material-icons">arrow_drop_down</span>
                                        </div>
                                        <div class="mui-select-dropdown" id="branchDropdown">
                                            <div class="mui-select-options" id="branchOptions">
                                                <!-- Options will be populated dynamically -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="credential-status" id="credentialStatus"></div>
                                <div class="error-message" id="branchError"></div>
                            </div>

                            <!-- Remember Me Checkbox -->
                            <div class="form-field">
                                <div class="remember-me-container">
                                    <label class="remember-me-label">
                                        <input type="checkbox" id="rememberMe" name="rememberMe" class="remember-me-checkbox">
                                        <span class="remember-me-checkmark"></span>
                                        <span class="remember-me-text">Remember me</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Login Button -->
                            <button type="submit" class="login-button" id="loginButton">
                                <span class="button-text">Login</span>
                                <div class="button-loader" id="buttonLoader" style="display: none;">
                                    <div class="spinner"></div>
                                </div>
                            </button>

                            <!-- Demo Credentials Info -->
                            <div class="demo-credentials-section">
                                <details class="demo-credentials">
                                    <summary class="demo-credentials-toggle">Demo Credentials</summary>
                                    <div class="demo-credentials-content">
                                        <div class="credential-item">
                                            <strong>Admin:</strong> admin / admin123<br>
                                            <small>Access: All branches</small>
                                        </div>
                                        <div class="credential-item">
                                            <strong>Manager:</strong> manager / manager789<br>
                                            <small>Access: Main, North, South</small>
                                        </div>
                                        <div class="credential-item">
                                            <strong>User 1:</strong> user1 / user123<br>
                                            <small>Access: Main, North</small>
                                        </div>
                                        <div class="credential-item">
                                            <strong>User 2:</strong> user2 / user456<br>
                                            <small>Access: South, East</small>
                                        </div>
                                    </div>
                                </details>
                            </div>

                            <!-- Forgot Password Link -->
                            <div class="forgot-password-section">
                                <a href="#" class="forgot-password-link" id="forgotPasswordLink">
                                    Forgot Password?
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <footer class="footer">
                <div class="footer-content">
                    <div class="footer-left">
                        <span>&copy; 2025 Prevost. All rights reserved.</span>
                        <span class="footer-separator">|</span>
                        <span class="version-text">Version 2.1.0</span>
                        <span class="footer-separator">|</span>
                        <span class="date-text">Released: January 2025</span>
                    </div>
                    <div class="footer-center">
                        <a href="#" class="footer-link">Privacy Policy</a>
                        <span class="footer-separator">|</span>
                        <a href="#" class="footer-link">Terms of Service</a>
                        <span class="footer-separator">|</span>
                        <a href="#" class="footer-link">Support</a>
                    </div>
                    <div class="footer-right">
                        <div class="contact-item">
                            <span class="material-icons">phone</span>
                            <span class="contact-text">******-PREVOST</span>
                        </div>
                        <span class="footer-separator">|</span>
                        <div class="contact-item">
                            <span class="material-icons">email</span>
                            <span class="contact-text"><EMAIL></span>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- Material-UI JavaScript -->
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="script.js"></script>
</body>
</html>

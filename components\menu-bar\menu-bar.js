/**
 * Menu Bar Component JavaScript
 * Handles dropdown menus, tab switching, and search functionality
 */

class MenuBarComponent {
    constructor() {
        this.activeDropdown = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeTabs();
        this.initializeSearch();
        console.log('Menu Bar Component initialized');
    }

    bindEvents() {
        // Menu button click handlers
        const menuButtons = document.querySelectorAll('.menu-button[data-dropdown]');
        menuButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleDropdown(button);
            });
        });

        // Icon button handlers
        const iconButtons = document.querySelectorAll('.icon-button[data-action]');
        iconButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const action = button.getAttribute('data-action');
                this.handleIconAction(action);
            });
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.menu-item')) {
                this.closeAllDropdowns();
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllDropdowns();
            }
        });
    }

    toggleDropdown(button) {
        const dropdownId = button.getAttribute('data-dropdown');
        const dropdown = document.getElementById(`${dropdownId}-menu`);
        const menuItem = button.closest('.menu-item');

        if (!dropdown) return;

        // Close other dropdowns
        if (this.activeDropdown && this.activeDropdown !== dropdown) {
            this.closeDropdown(this.activeDropdown);
        }

        // Toggle current dropdown
        const isActive = dropdown.classList.contains('active');
        
        if (isActive) {
            this.closeDropdown(dropdown);
        } else {
            this.openDropdown(dropdown, menuItem);
        }
    }

    openDropdown(dropdown, menuItem) {
        dropdown.classList.add('active');
        menuItem.classList.add('active');
        this.activeDropdown = dropdown;

        // Set ARIA attributes
        const button = menuItem.querySelector('.menu-button');
        button.setAttribute('aria-expanded', 'true');
    }

    closeDropdown(dropdown) {
        if (!dropdown) return;

        dropdown.classList.remove('active');
        const menuItem = dropdown.closest('.menu-item') || 
                        document.querySelector(`[data-dropdown="${dropdown.id.replace('-menu', '')}"]`)?.closest('.menu-item');
        
        if (menuItem) {
            menuItem.classList.remove('active');
            const button = menuItem.querySelector('.menu-button');
            button.setAttribute('aria-expanded', 'false');
        }

        if (this.activeDropdown === dropdown) {
            this.activeDropdown = null;
        }
    }

    closeAllDropdowns() {
        const activeDropdowns = document.querySelectorAll('.dropdown-menu.active');
        activeDropdowns.forEach(dropdown => {
            this.closeDropdown(dropdown);
        });
    }

    initializeTabs() {
        const tabButtons = document.querySelectorAll('.mega-menu-tab');
        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchTab(button);
            });
        });
    }

    switchTab(activeButton) {
        const tabId = activeButton.getAttribute('data-tab');
        const megaMenu = activeButton.closest('.dropdown-menu');
        
        if (!megaMenu) return;

        // Remove active class from all tabs in this menu
        const allTabs = megaMenu.querySelectorAll('.mega-menu-tab');
        const allTabContents = megaMenu.querySelectorAll('.mega-menu-tab-content');

        allTabs.forEach(tab => tab.classList.remove('active'));
        allTabContents.forEach(content => content.classList.remove('active'));

        // Add active class to clicked tab
        activeButton.classList.add('active');

        // Show corresponding content
        const targetContent = megaMenu.querySelector(`#${tabId}-content`);
        if (targetContent) {
            targetContent.classList.add('active');
        }
    }

    initializeSearch() {
        const searchInputs = document.querySelectorAll('.mega-menu-search-input');
        searchInputs.forEach(input => {
            const clearButton = input.parentElement.querySelector('.mega-menu-search-clear');
            
            input.addEventListener('input', (e) => {
                this.handleSearch(e.target, clearButton);
            });

            if (clearButton) {
                clearButton.addEventListener('click', () => {
                    this.clearSearch(input, clearButton);
                });
            }
        });
    }

    handleSearch(input, clearButton) {
        const searchTerm = input.value.toLowerCase().trim();
        const megaMenu = input.closest('.dropdown-menu');
        
        if (!megaMenu) return;

        // Show/hide clear button
        if (clearButton) {
            clearButton.style.display = searchTerm ? 'block' : 'none';
        }

        // Filter menu items
        const cards = megaMenu.querySelectorAll('.mega-card');
        let hasVisibleItems = false;

        cards.forEach(card => {
            const title = card.querySelector('h4')?.textContent.toLowerCase() || '';
            const description = card.querySelector('p')?.textContent.toLowerCase() || '';
            const isMatch = title.includes(searchTerm) || description.includes(searchTerm);
            
            card.style.display = isMatch ? 'flex' : 'none';
            if (isMatch) hasVisibleItems = true;
        });

        // Show "No results" message if needed
        this.toggleNoResultsMessage(megaMenu, !hasVisibleItems && searchTerm);
    }

    clearSearch(input, clearButton) {
        input.value = '';
        if (clearButton) {
            clearButton.style.display = 'none';
        }
        this.handleSearch(input, clearButton);
        input.focus();
    }

    toggleNoResultsMessage(megaMenu, show) {
        let noResultsMsg = megaMenu.querySelector('.no-results-message');
        
        if (show && !noResultsMsg) {
            noResultsMsg = document.createElement('div');
            noResultsMsg.className = 'no-results-message';
            noResultsMsg.innerHTML = `
                <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                    <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <p>No items found matching your search.</p>
                </div>
            `;
            const content = megaMenu.querySelector('.mega-menu-content');
            if (content) content.appendChild(noResultsMsg);
        } else if (!show && noResultsMsg) {
            noResultsMsg.remove();
        }
    }

    handleIconAction(action) {
        console.log(`Icon action triggered: ${action}`);
        
        // Dispatch custom event for external handling
        const event = new CustomEvent('menuIconAction', {
            detail: { action }
        });
        document.dispatchEvent(event);

        // Default actions (can be customized)
        switch (action) {
            case 'dashboard-settings':
                this.showMessage('Dashboard Settings clicked');
                break;
            case 'load-unload-dashboard':
                this.showMessage('Load/Unload Dashboard clicked');
                break;
            case 'kpi-reports':
                this.showMessage('KPI Reports clicked');
                break;
            case 'month-end-process':
                this.showMessage('Month End Process clicked');
                break;
            case 'user-manual':
                this.showMessage('User Manual clicked');
                break;
            default:
                console.log(`Unknown action: ${action}`);
        }
    }

    showMessage(message) {
        // Simple notification system (can be replaced with your preferred notification library)
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--accent-color);
            color: var(--menu-text);
            padding: 1rem 1.5rem;
            border-radius: 6px;
            box-shadow: 0 4px 12px var(--shadow-medium);
            z-index: 10000;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Public API methods
    openMenu(menuId) {
        const button = document.querySelector(`[data-dropdown="${menuId}"]`);
        if (button) {
            this.toggleDropdown(button);
        }
    }

    closeMenu(menuId) {
        const dropdown = document.getElementById(`${menuId}-menu`);
        if (dropdown) {
            this.closeDropdown(dropdown);
        }
    }

    switchToTab(menuId, tabId) {
        const dropdown = document.getElementById(`${menuId}-menu`);
        if (dropdown) {
            const tabButton = dropdown.querySelector(`[data-tab="${tabId}"]`);
            if (tabButton) {
                this.switchTab(tabButton);
            }
        }
    }
}

// Initialize the component when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.menuBarComponent = new MenuBarComponent();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MenuBarComponent;
}

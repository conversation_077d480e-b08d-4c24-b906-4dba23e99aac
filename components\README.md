# Reusable Web Components

This collection contains three modular, portable web components extracted from a comprehensive admin dashboard. Each component is designed to be easily integrated into other web projects without conflicts or missing dependencies.

## Components Overview

### 1. Menu Bar Component
**Location**: `components/menu-bar/`

A responsive navigation menu with dropdown mega-menus, tab switching, and search functionality.

**Key Features**:
- Responsive mega-menu dropdowns
- Tab navigation within menus
- Real-time search within menu items
- Icon action buttons
- Keyboard navigation (ESC to close)
- Theme support via CSS custom properties

**Files**:
- `menu-bar.html` - Complete HTML structure
- `menu-bar.css` - Styling and responsive design
- `menu-bar.js` - Interactive functionality
- `README.md` - Detailed documentation

### 2. Search Bar Component
**Location**: `components/search-bar/`

A powerful search component with real-time results, keyboard navigation, and recent search history.

**Key Features**:
- Real-time search with intelligent scoring
- Keyboard shortcuts (Ctrl+K to focus)
- Recent search history with local storage
- Arrow key navigation through results
- Customizable search data and templates
- Responsive design with overlay

**Files**:
- `search-bar.html` - Complete HTML structure
- `search-bar.css` - Styling and responsive design
- `search-bar.js` - Search logic and interactions
- `README.md` - Detailed documentation

### 3. Sidebar Component
**Location**: `components/sidebar/`

Collapsible sidebars with bookmark management, usage tracking, and local storage persistence.

**Key Features**:
- Dual left/right collapsible sidebars
- Bookmark management with categories
- Usage tracking and frequency sorting
- Local storage persistence
- Modal interfaces for management
- Custom events for integration

**Files**:
- `sidebar.html` - Complete HTML structure
- `sidebar.css` - Styling and animations
- `sidebar.js` - Bookmark management logic
- `README.md` - Detailed documentation

## Quick Integration Guide

### 1. Choose Your Components

Select the components you need for your project. Each component is completely independent and can be used separately or together.

### 2. Include Dependencies

All components require:
- **Font Awesome 6.4.0+** for icons
- **Modern browser** with ES6+ support

```html
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
```

### 3. Include Component Files

For each component you want to use:

```html
<!-- Component CSS -->
<link rel="stylesheet" href="path/to/component/component.css">

<!-- Component HTML (copy the structure you need) -->
<!-- ... component HTML ... -->

<!-- Component JavaScript -->
<script src="path/to/component/component.js"></script>
```

### 4. Initialize Components

Components auto-initialize when the DOM loads, but you can also initialize manually:

```javascript
// Auto-initialization (default)
// Components initialize automatically on DOMContentLoaded

// Manual initialization
const menuBar = new MenuBarComponent();
const searchBar = new SearchBarComponent({
    searchData: yourSearchData
});
const sidebar = new SidebarComponent({
    bookmarks: yourBookmarks
});
```

## Global Integration Example

Here's how to integrate all three components together:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Application</title>
    
    <!-- Dependencies -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Component Styles -->
    <link rel="stylesheet" href="components/search-bar/search-bar.css">
    <link rel="stylesheet" href="components/menu-bar/menu-bar.css">
    <link rel="stylesheet" href="components/sidebar/sidebar.css">
    
    <style>
        /* Adjust layout for all components */
        body {
            margin: 0;
            padding: 0;
        }
        
        .main-content {
            margin-top: 130px; /* Space for search header + menu bar */
            padding: 2rem;
        }
    </style>
</head>
<body>
    <!-- Search Bar Component -->
    <div class="search-bar-component">
        <!-- Search bar HTML structure -->
    </div>
    
    <!-- Menu Bar Component -->
    <nav class="menu-bar">
        <!-- Menu bar HTML structure -->
    </nav>
    
    <!-- Sidebar Component -->
    <div class="sidebar-component">
        <!-- Sidebar HTML structure -->
    </div>
    
    <!-- Your Main Content -->
    <main class="main-content">
        <h1>Your Application Content</h1>
        <p>This is where your main application content goes.</p>
    </main>
    
    <!-- Component Scripts -->
    <script src="components/search-bar/search-bar.js"></script>
    <script src="components/menu-bar/menu-bar.js"></script>
    <script src="components/sidebar/sidebar.js"></script>
    
    <!-- Integration Script -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Components are auto-initialized
            
            // Optional: Listen for component events
            document.addEventListener('searchResultSelected', (event) => {
                console.log('Search result selected:', event.detail.item);
            });
            
            document.addEventListener('menuIconAction', (event) => {
                console.log('Menu icon action:', event.detail.action);
            });
            
            document.addEventListener('bookmarkSelected', (event) => {
                console.log('Bookmark selected:', event.detail.bookmark);
            });
        });
    </script>
</body>
</html>
```

## Customization

### Theme Consistency

All components use CSS custom properties for theming. Set these once to theme all components:

```css
:root {
    /* Primary colors */
    --primary-color: #your-primary;
    --secondary-color: #your-secondary;
    --accent-color: #your-accent;
    --accent-hover: #your-accent-hover;
    
    /* Background colors */
    --background-primary: #your-bg-primary;
    --background-secondary: #your-bg-secondary;
    
    /* Text colors */
    --text-primary: #your-text-primary;
    --text-secondary: #your-text-secondary;
    --text-hint: #your-text-hint;
    
    /* Border and shadow */
    --border-color: #your-border;
    --shadow-light: rgba(0, 0, 0, 0.12);
    --shadow-medium: rgba(0, 0, 0, 0.16);
    --shadow-dark: rgba(0, 0, 0, 0.24);
}
```

### Component Communication

Components can communicate through custom events:

```javascript
// Search component triggers navigation
document.addEventListener('searchResultSelected', (event) => {
    const item = event.detail.item;
    
    // Add to sidebar bookmarks
    if (window.sidebarComponent) {
        window.sidebarComponent.addBookmark({
            name: item.name,
            url: item.href,
            icon: item.icon,
            category: 'recent',
            description: item.description
        });
    }
});

// Menu component triggers search
document.addEventListener('menuIconAction', (event) => {
    if (event.detail.action === 'search') {
        if (window.searchBarComponent) {
            window.searchBarComponent.focusSearch();
        }
    }
});
```

## Browser Support

All components support:
- **Chrome**: 60+
- **Firefox**: 60+
- **Safari**: 12+
- **Edge**: 79+

## Performance Considerations

1. **Lazy Loading**: Load component data asynchronously for large datasets
2. **Debouncing**: Components include built-in debouncing for search and interactions
3. **Memory Management**: Components clean up event listeners appropriately
4. **CSS Optimization**: Use CSS custom properties for efficient theme switching

## Accessibility

All components include:
- ARIA labels and roles
- Keyboard navigation
- Focus management
- Screen reader support
- High contrast compatibility

## Framework Integration

### React Integration

```jsx
import { useEffect } from 'react';

function App() {
    useEffect(() => {
        // Components auto-initialize
        // Add any custom integration logic here
    }, []);
    
    return (
        <div className="app">
            {/* Component HTML structures */}
        </div>
    );
}
```

### Vue Integration

```vue
<template>
    <div class="app">
        <!-- Component HTML structures -->
    </div>
</template>

<script>
export default {
    mounted() {
        // Components auto-initialize
        // Add any custom integration logic here
    }
}
</script>
```

### Angular Integration

```typescript
import { Component, OnInit } from '@angular/core';

@Component({
    selector: 'app-root',
    template: `
        <div class="app">
            <!-- Component HTML structures -->
        </div>
    `
})
export class AppComponent implements OnInit {
    ngOnInit() {
        // Components auto-initialize
        // Add any custom integration logic here
    }
}
```

## License

These components are provided as-is for educational and development purposes. Feel free to modify and use them in your projects.

## Support

Each component includes comprehensive documentation in its respective README file. For integration questions, refer to the individual component documentation or the examples provided above.

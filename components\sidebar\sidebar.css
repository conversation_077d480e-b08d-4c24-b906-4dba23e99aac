/* Sidebar Component CSS */

/* CSS Custom Properties */
:root {
    /* Primary colors */
    --primary-color: #000000;
    --secondary-color: #ffffff;
    --accent-color: #000000;
    --accent-hover: #333333;
    --background-primary: #ffffff;
    --background-secondary: #f8f9fa;
    --text-primary: #000000;
    --text-secondary: #666666;
    --text-hint: #999999;
    --border-color: #cccccc;
    --shadow-light: rgba(0, 0, 0, 0.12);
    --shadow-medium: rgba(0, 0, 0, 0.16);
    --shadow-dark: rgba(0, 0, 0, 0.24);
    --input-background: #ffffff;
    --input-border: #cccccc;
    --input-focus: #000000;

    /* Sidebar specific colors */
    --sidebar-background: #ffffff;
    --sidebar-border: #e0e0e0;
    --sidebar-header-background: #f8f9fa;
    --sidebar-item-hover: #f0f0f0;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Roboto', sans-serif;
}

body {
    background: var(--background-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Sidebar Component Container */
.sidebar-component {
    position: relative;
    width: 100%;
    height: 100vh;
}

/* Sidebar Base Styles */
.sidebar {
    position: fixed;
    top: 0;
    width: 320px;
    height: 100vh;
    background: var(--sidebar-background);
    border: 1px solid var(--sidebar-border);
    box-shadow: 0 4px 20px var(--shadow-medium);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-left {
    left: 0;
    border-right: 1px solid var(--sidebar-border);
}

.sidebar-right {
    right: 0;
    transform: translateX(100%);
    border-left: 1px solid var(--sidebar-border);
}

.sidebar.open {
    transform: translateX(0);
}

/* Sidebar Header */
.sidebar-header {
    padding: 1.5rem;
    background: var(--sidebar-header-background);
    border-bottom: 1px solid var(--sidebar-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.sidebar-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.sidebar-title i {
    color: var(--accent-color);
}

.sidebar-close {
    background: none;
    border: none;
    color: var(--text-hint);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 1.1rem;
}

.sidebar-close:hover {
    background: var(--sidebar-item-hover);
    color: var(--text-primary);
}

/* Sidebar Content */
.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

.sidebar-content::-webkit-scrollbar {
    width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
    background: var(--background-secondary);
}

.sidebar-content::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
    background: var(--text-hint);
}

/* Bookmark Sections */
.bookmark-section {
    margin-bottom: 2rem;
}

.bookmark-section:last-child {
    margin-bottom: 0;
}

.bookmark-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1.5rem 0.75rem;
    border-bottom: 1px solid var(--sidebar-border);
    margin-bottom: 1rem;
}

.bookmark-header h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bookmark-manage-btn {
    background: none;
    border: none;
    color: var(--text-hint);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.bookmark-manage-btn:hover {
    background: var(--sidebar-item-hover);
    color: var(--text-primary);
}

/* Bookmark Lists */
.bookmark-list,
.quick-access-list,
.category-list {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 0 1rem;
}

.bookmark-item,
.quick-access-item,
.category-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0.75rem;
    text-decoration: none;
    color: var(--text-primary);
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.bookmark-item:hover,
.quick-access-item:hover,
.category-item:hover {
    background: var(--sidebar-item-hover);
    transform: translateX(4px);
}

.bookmark-item-icon,
.quick-access-item-icon,
.category-item-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--accent-color);
    color: var(--secondary-color);
    border-radius: 8px;
    font-size: 1rem;
    flex-shrink: 0;
}

.bookmark-item-content,
.quick-access-item-content,
.category-item-content {
    flex: 1;
    min-width: 0;
}

.bookmark-item-title,
.quick-access-item-title,
.category-item-title {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.bookmark-item-description,
.quick-access-item-description,
.category-item-description {
    font-size: 0.8rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.bookmark-item-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
    flex-shrink: 0;
}

.bookmark-usage {
    font-size: 0.75rem;
    color: var(--text-hint);
    background: var(--background-secondary);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
}

.bookmark-remove {
    background: none;
    border: none;
    color: var(--text-hint);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    opacity: 0;
}

.bookmark-item:hover .bookmark-remove {
    opacity: 1;
}

.bookmark-remove:hover {
    background: #ff4757;
    color: white;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--sidebar-border);
    background: var(--sidebar-header-background);
    flex-shrink: 0;
}

.sidebar-btn {
    width: 100%;
    background: var(--accent-color);
    color: var(--secondary-color);
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.sidebar-btn:hover {
    background: var(--accent-hover);
    transform: translateY(-1px);
}

/* Sidebar Overlay */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Sidebar Indicators */
.sidebar-indicators {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    z-index: 998;
    pointer-events: none;
}

.sidebar-indicator {
    position: absolute;
    background: var(--accent-color);
    color: var(--secondary-color);
    border: none;
    width: 40px;
    height: 60px;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    pointer-events: auto;
    opacity: 0.7;
}

.sidebar-indicator:hover {
    opacity: 1;
    transform: translateX(4px);
}

.sidebar-indicator-left {
    left: 0;
}

.sidebar-indicator-right {
    right: 0;
    border-radius: 8px 0 0 8px;
}

.sidebar-indicator-right:hover {
    transform: translateX(-4px);
}

/* Modal Styles */
.bookmark-modal,
.add-bookmark-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 10000;
}

.bookmark-modal.active,
.add-bookmark-modal.active {
    opacity: 1;
    visibility: visible;
}

.bookmark-modal-content,
.add-bookmark-modal-content {
    background: var(--sidebar-background);
    border-radius: 12px;
    box-shadow: 0 20px 60px var(--shadow-dark);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.bookmark-modal.active .bookmark-modal-content,
.add-bookmark-modal.active .add-bookmark-modal-content {
    transform: scale(1);
}

.bookmark-modal-header,
.add-bookmark-modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--sidebar-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--sidebar-header-background);
}

.bookmark-modal-header h3,
.add-bookmark-modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.bookmark-modal-close,
.add-bookmark-modal-close {
    background: none;
    border: none;
    color: var(--text-hint);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 1.1rem;
}

.bookmark-modal-close:hover,
.add-bookmark-modal-close:hover {
    background: var(--sidebar-item-hover);
    color: var(--text-primary);
}

.bookmark-modal-body,
.add-bookmark-modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
}

.bookmark-modal-footer,
.add-bookmark-modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--sidebar-border);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    background: var(--sidebar-header-background);
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--input-border);
    border-radius: 6px;
    background: var(--input-background);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--input-focus);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--accent-color);
    color: var(--secondary-color);
}

.btn-primary:hover {
    background: var(--accent-hover);
}

.btn-secondary {
    background: var(--background-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--sidebar-item-hover);
}

/* Search Styles */
.bookmark-search {
    position: relative;
    margin-bottom: 1.5rem;
}

.bookmark-search input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--input-border);
    border-radius: 8px;
    background: var(--input-background);
    color: var(--text-primary);
    font-size: 0.95rem;
}

.bookmark-search i {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-hint);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 280px;
    }
    
    .sidebar-indicator {
        width: 35px;
        height: 50px;
    }
    
    .bookmark-modal-content,
    .add-bookmark-modal-content {
        width: 95%;
        margin: 1rem;
    }
}

# Sidebar Component

A responsive, collapsible sidebar component with bookmark management, local storage, and customizable categories.

## Features

- **Dual Sidebars**: Left and right collapsible sidebars
- **Bookmark Management**: Add, remove, and organize bookmarks
- **Usage Tracking**: Automatically tracks bookmark usage frequency
- **Local Storage**: Persists bookmarks across browser sessions
- **Categories**: Organize bookmarks into customizable categories
- **Responsive Design**: Works on all screen sizes
- **Modal Interfaces**: User-friendly modals for bookmark management
- **Keyboard Support**: ESC key to close sidebars and modals
- **Custom Events**: Dispatches events for external integration

## Files

- `sidebar.html` - Complete HTML structure with sample data
- `sidebar.css` - All styling including animations and responsive design
- `sidebar.js` - JavaScript functionality and bookmark management
- `README.md` - This documentation file

## Dependencies

- **Font Awesome 6.4.0+** - For icons
- **Modern Browser** - Supports ES6+, localStorage, and CSS Grid

## Quick Start

### 1. Include Required Files

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Page</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Sidebar Component CSS -->
    <link rel="stylesheet" href="path/to/sidebar.css">
</head>
<body>
    <!-- Your sidebar HTML here -->
    
    <!-- Sidebar Component JavaScript -->
    <script src="path/to/sidebar.js"></script>
</body>
</html>
```

### 2. Basic HTML Structure

```html
<div class="sidebar-component">
    <!-- Left Sidebar -->
    <aside class="sidebar sidebar-left" id="sidebar-left">
        <div class="sidebar-header">
            <h3 class="sidebar-title">
                <i class="fas fa-star"></i>
                <span>Favorites</span>
            </h3>
            <button class="sidebar-close" id="sidebar-left-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="sidebar-content">
            <!-- Content will be populated by JavaScript -->
        </div>
    </aside>

    <!-- Toggle Indicators -->
    <div class="sidebar-indicators">
        <button class="sidebar-indicator sidebar-indicator-left" id="sidebar-toggle-left">
            <i class="fas fa-chevron-right"></i>
        </button>
        <button class="sidebar-indicator sidebar-indicator-right" id="sidebar-toggle-right">
            <i class="fas fa-chevron-left"></i>
        </button>
    </div>

    <!-- Overlay -->
    <div class="sidebar-overlay" id="sidebar-overlay"></div>
</div>
```

### 3. Provide Initial Data

```javascript
// Define your bookmark data
window.sampleBookmarks = [
    {
        id: 'dashboard',
        name: 'Dashboard',
        icon: 'fas fa-tachometer-alt',
        href: '/dashboard',
        category: 'favorites',
        description: 'Main dashboard overview',
        usage: 25
    },
    // ... more bookmarks
];

window.sampleCategories = [
    { id: 'favorites', name: 'Favorites', icon: 'fas fa-star' },
    { id: 'recent', name: 'Recent', icon: 'fas fa-clock' },
    // ... more categories
];
```

## Configuration

### Initialize with Custom Options

```javascript
const sidebar = new SidebarComponent({
    bookmarks: yourBookmarkData,
    categories: yourCategoryData,
    storageKey: 'my-app-bookmarks',
    autoSave: true
});
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `bookmarks` | Array | `[]` | Initial bookmark data |
| `categories` | Array | `[]` | Available categories |
| `storageKey` | String | `'sidebar-bookmarks'` | localStorage key |
| `autoSave` | Boolean | `true` | Enable automatic saving |

## Data Formats

### Bookmark Object

```javascript
{
    id: 'unique-id',                    // Required: Unique identifier
    name: 'Bookmark Name',              // Required: Display name
    href: '/path/to/page',              // Required: Navigation URL
    icon: 'fas fa-icon-name',           // Optional: Font Awesome icon
    category: 'category-id',            // Optional: Category ID
    description: 'Description text',    // Optional: Description
    usage: 0,                          // Optional: Usage count
    dateAdded: '2023-01-01T00:00:00Z', // Optional: Date added
    lastUsed: '2023-01-01T00:00:00Z'   // Optional: Last used date
}
```

### Category Object

```javascript
{
    id: 'category-id',           // Required: Unique identifier
    name: 'Category Name',       // Required: Display name
    icon: 'fas fa-folder'        // Optional: Font Awesome icon
}
```

## JavaScript API

### Accessing the Component

```javascript
// Component is available globally after initialization
const sidebar = window.sidebarComponent;
```

### Methods

```javascript
// Toggle sidebars
sidebar.toggleLeft();
sidebar.toggleRight();

// Open/close specific sidebars
sidebar.openLeftSidebar();
sidebar.closeLeftSidebar();
sidebar.openRightSidebar();
sidebar.closeRightSidebar();

// Bookmark management
sidebar.addBookmark({
    name: 'New Bookmark',
    url: '/new-page',
    icon: 'fas fa-star',
    category: 'favorites',
    description: 'A new bookmark'
});

sidebar.removeBookmark('bookmark-id');

// Data management
sidebar.updateBookmarks(newBookmarkArray);
const bookmarks = sidebar.getBookmarks();

// Modal management
sidebar.openManageModal();
sidebar.closeManageModal();
sidebar.openAddBookmarkModal();
sidebar.closeAddBookmarkModal();
```

### Events

Listen for sidebar events:

```javascript
// Bookmark selected
document.addEventListener('bookmarkSelected', (event) => {
    const bookmark = event.detail.bookmark;
    console.log('Bookmark selected:', bookmark);
    
    // Custom navigation logic
    if (bookmark.category === 'external') {
        window.open(bookmark.href, '_blank');
    } else {
        window.location.href = bookmark.href;
    }
});

// Bookmark added
document.addEventListener('bookmarkAdded', (event) => {
    const bookmark = event.detail.bookmark;
    console.log('Bookmark added:', bookmark);
});

// Bookmark removed
document.addEventListener('bookmarkRemoved', (event) => {
    const bookmarkId = event.detail.bookmarkId;
    console.log('Bookmark removed:', bookmarkId);
});
```

## Customization

### Styling

Override CSS custom properties:

```css
:root {
    --sidebar-background: #your-bg-color;
    --sidebar-border: #your-border-color;
    --accent-color: #your-accent-color;
    --sidebar-item-hover: #your-hover-color;
    /* ... other properties */
}
```

### Custom Bookmark Template

Override the `createBookmarkElement` method:

```javascript
class CustomSidebar extends SidebarComponent {
    createBookmarkElement(bookmark) {
        const element = document.createElement('button');
        element.className = 'bookmark-item';
        
        // Your custom template
        element.innerHTML = `
            <div class="custom-bookmark">
                <h4>${bookmark.name}</h4>
                <p>${bookmark.description}</p>
                <span class="usage-count">${bookmark.usage || 0}</span>
            </div>
        `;
        
        element.addEventListener('click', () => {
            this.navigateToBookmark(bookmark);
        });
        
        return element;
    }
}
```

### Custom Categories

```javascript
const customCategories = [
    { id: 'work', name: 'Work', icon: 'fas fa-briefcase' },
    { id: 'personal', name: 'Personal', icon: 'fas fa-user' },
    { id: 'tools', name: 'Tools', icon: 'fas fa-tools' },
    { id: 'docs', name: 'Documentation', icon: 'fas fa-book' }
];

const sidebar = new SidebarComponent({
    categories: customCategories
});
```

## Integration Examples

### With React

```jsx
import { useEffect, useRef } from 'react';

function SidebarWrapper({ bookmarks, categories }) {
    const sidebarRef = useRef();
    
    useEffect(() => {
        const sidebar = new SidebarComponent({
            bookmarks: bookmarks,
            categories: categories
        });
        
        sidebarRef.current = sidebar;
        
        return () => {
            // Cleanup if needed
        };
    }, []);
    
    useEffect(() => {
        if (sidebarRef.current) {
            sidebarRef.current.updateBookmarks(bookmarks);
        }
    }, [bookmarks]);
    
    return (
        <div className="sidebar-component">
            {/* Sidebar HTML */}
        </div>
    );
}
```

### With Vue

```vue
<template>
    <div class="sidebar-component">
        <!-- Sidebar HTML -->
    </div>
</template>

<script>
export default {
    props: ['bookmarks', 'categories'],
    data() {
        return {
            sidebar: null
        };
    },
    mounted() {
        this.sidebar = new SidebarComponent({
            bookmarks: this.bookmarks,
            categories: this.categories
        });
    },
    watch: {
        bookmarks(newBookmarks) {
            if (this.sidebar) {
                this.sidebar.updateBookmarks(newBookmarks);
            }
        }
    }
}
</script>
```

## Keyboard Shortcuts

- **ESC**: Close all open sidebars and modals

## Browser Support

- **Chrome**: 60+
- **Firefox**: 60+
- **Safari**: 12+
- **Edge**: 79+

## Performance Tips

1. **Limit Bookmarks**: Keep bookmark count under 100 for best performance
2. **Lazy Loading**: Load bookmark data asynchronously if large
3. **Debounce Updates**: Debounce frequent bookmark updates
4. **Virtual Scrolling**: Consider for very large bookmark lists

## Accessibility Features

- ARIA labels and roles
- Keyboard navigation
- Focus management
- Screen reader support
- High contrast support

## License

This component is provided as-is for educational and development purposes.

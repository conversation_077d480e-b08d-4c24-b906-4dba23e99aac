<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar Component</title>
    <link rel="stylesheet" href="sidebar.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Sidebar Component -->
    <div class="sidebar-component">
        <!-- Left Sidebar -->
        <aside class="sidebar sidebar-left" id="sidebar-left">
            <div class="sidebar-header">
                <h3 class="sidebar-title">
                    <i class="fas fa-star"></i>
                    <span>Favorites</span>
                </h3>
                <button class="sidebar-close" id="sidebar-left-close" aria-label="Close sidebar">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="sidebar-content">
                <div class="bookmark-section">
                    <div class="bookmark-header">
                        <h4>Frequently Used</h4>
                        <button class="bookmark-manage-btn" id="manage-bookmarks" title="Manage Bookmarks">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                    <div class="bookmark-list" id="bookmark-list">
                        <!-- Bookmarks will be populated by JavaScript -->
                    </div>
                </div>

                <div class="bookmark-section">
                    <div class="bookmark-header">
                        <h4>Quick Access</h4>
                    </div>
                    <div class="quick-access-list" id="quick-access-list">
                        <!-- Quick access items will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <div class="sidebar-footer">
                <button class="sidebar-btn" id="add-bookmark-btn">
                    <i class="fas fa-plus"></i>
                    <span>Add Bookmark</span>
                </button>
            </div>
        </aside>

        <!-- Right Sidebar -->
        <aside class="sidebar sidebar-right" id="sidebar-right">
            <div class="sidebar-header">
                <h3 class="sidebar-title">
                    <i class="fas fa-bookmark"></i>
                    <span>Bookmarks</span>
                </h3>
                <button class="sidebar-close" id="sidebar-right-close" aria-label="Close sidebar">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="sidebar-content">
                <div class="bookmark-section">
                    <div class="bookmark-header">
                        <h4>Recent Items</h4>
                        <button class="bookmark-manage-btn" id="manage-bookmarks-right" title="Manage Bookmarks">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                    <div class="bookmark-list" id="bookmark-list-right">
                        <!-- Bookmarks will be populated by JavaScript -->
                    </div>
                </div>

                <div class="bookmark-section">
                    <div class="bookmark-header">
                        <h4>Categories</h4>
                    </div>
                    <div class="category-list" id="category-list">
                        <!-- Categories will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <div class="sidebar-footer">
                <button class="sidebar-btn" id="add-bookmark-btn-right">
                    <i class="fas fa-plus"></i>
                    <span>Add Bookmark</span>
                </button>
            </div>
        </aside>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebar-overlay"></div>

        <!-- Sidebar Toggle Indicators -->
        <div class="sidebar-indicators">
            <button class="sidebar-indicator sidebar-indicator-left" id="sidebar-toggle-left" title="Open Left Sidebar">
                <i class="fas fa-chevron-right"></i>
            </button>
            <button class="sidebar-indicator sidebar-indicator-right" id="sidebar-toggle-right" title="Open Right Sidebar">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>

        <!-- Bookmark Management Modal -->
        <div class="bookmark-modal" id="bookmark-modal">
            <div class="bookmark-modal-content">
                <div class="bookmark-modal-header">
                    <h3>Manage Bookmarks</h3>
                    <button class="bookmark-modal-close" id="bookmark-modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="bookmark-modal-body">
                    <div class="bookmark-search">
                        <input type="text" id="bookmark-search" placeholder="Search items to bookmark...">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="bookmark-categories" id="bookmark-categories">
                        <!-- Categories will be populated by JavaScript -->
                    </div>
                </div>
                <div class="bookmark-modal-footer">
                    <button class="btn btn-secondary" id="bookmark-modal-cancel">Cancel</button>
                    <button class="btn btn-primary" id="bookmark-modal-save">Save Changes</button>
                </div>
            </div>
        </div>

        <!-- Add Bookmark Modal -->
        <div class="add-bookmark-modal" id="add-bookmark-modal">
            <div class="add-bookmark-modal-content">
                <div class="add-bookmark-modal-header">
                    <h3>Add New Bookmark</h3>
                    <button class="add-bookmark-modal-close" id="add-bookmark-modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="add-bookmark-modal-body">
                    <form id="add-bookmark-form">
                        <div class="form-group">
                            <label for="bookmark-name">Name</label>
                            <input type="text" id="bookmark-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="bookmark-url">URL</label>
                            <input type="url" id="bookmark-url" name="url" required>
                        </div>
                        <div class="form-group">
                            <label for="bookmark-icon">Icon (Font Awesome class)</label>
                            <input type="text" id="bookmark-icon" name="icon" placeholder="fas fa-star">
                        </div>
                        <div class="form-group">
                            <label for="bookmark-category">Category</label>
                            <select id="bookmark-category" name="category">
                                <option value="favorites">Favorites</option>
                                <option value="recent">Recent</option>
                                <option value="tools">Tools</option>
                                <option value="reports">Reports</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="bookmark-description">Description</label>
                            <textarea id="bookmark-description" name="description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="add-bookmark-modal-footer">
                    <button class="btn btn-secondary" id="add-bookmark-cancel">Cancel</button>
                    <button class="btn btn-primary" id="add-bookmark-save">Add Bookmark</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Sample Data for Demo -->
    <script>
        // Sample bookmark data - replace with your actual data source
        window.sampleBookmarks = [
            {
                id: 'dashboard',
                name: 'Dashboard',
                icon: 'fas fa-tachometer-alt',
                href: '/dashboard',
                category: 'favorites',
                description: 'Main dashboard overview',
                usage: 25
            },
            {
                id: 'users',
                name: 'User Management',
                icon: 'fas fa-users',
                href: '/users',
                category: 'favorites',
                description: 'Manage system users',
                usage: 18
            },
            {
                id: 'reports',
                name: 'Analytics Reports',
                icon: 'fas fa-chart-bar',
                href: '/reports',
                category: 'reports',
                description: 'View analytics and reports',
                usage: 12
            },
            {
                id: 'settings',
                name: 'Settings',
                icon: 'fas fa-cog',
                href: '/settings',
                category: 'tools',
                description: 'System configuration',
                usage: 8
            },
            {
                id: 'tickets',
                name: 'Support Tickets',
                icon: 'fas fa-ticket-alt',
                href: '/tickets',
                category: 'recent',
                description: 'Manage support tickets',
                usage: 15
            }
        ];

        window.sampleCategories = [
            { id: 'favorites', name: 'Favorites', icon: 'fas fa-star' },
            { id: 'recent', name: 'Recent', icon: 'fas fa-clock' },
            { id: 'tools', name: 'Tools', icon: 'fas fa-tools' },
            { id: 'reports', name: 'Reports', icon: 'fas fa-chart-line' }
        ];
    </script>

    <script src="sidebar.js"></script>
</body>
</html>

/**
 * Sidebar Component JavaScript
 * Handles collapsible sidebars with bookmark management functionality
 */

class SidebarComponent {
    constructor(options = {}) {
        this.options = {
            bookmarks: options.bookmarks || window.sampleBookmarks || [],
            categories: options.categories || window.sampleCategories || [],
            storageKey: options.storageKey || 'sidebar-bookmarks',
            autoSave: options.autoSave !== false,
            ...options
        };

        this.leftSidebarOpen = false;
        this.rightSidebarOpen = false;
        this.bookmarks = this.loadBookmarks();
        this.categories = this.options.categories;

        this.init();
    }

    init() {
        this.bindElements();
        this.bindEvents();
        this.populateSidebars();
        console.log('Sidebar Component initialized');
    }

    bindElements() {
        // Sidebars
        this.sidebarLeft = document.getElementById('sidebar-left');
        this.sidebarRight = document.getElementById('sidebar-right');
        this.sidebarOverlay = document.getElementById('sidebar-overlay');

        // Toggle buttons
        this.toggleLeft = document.getElementById('sidebar-toggle-left');
        this.toggleRight = document.getElementById('sidebar-toggle-right');

        // Close buttons
        this.closeLeft = document.getElementById('sidebar-left-close');
        this.closeRight = document.getElementById('sidebar-right-close');

        // Bookmark lists
        this.bookmarkList = document.getElementById('bookmark-list');
        this.bookmarkListRight = document.getElementById('bookmark-list-right');
        this.quickAccessList = document.getElementById('quick-access-list');
        this.categoryList = document.getElementById('category-list');

        // Manage buttons
        this.manageBookmarksBtn = document.getElementById('manage-bookmarks');
        this.manageBookmarksRightBtn = document.getElementById('manage-bookmarks-right');

        // Add bookmark buttons
        this.addBookmarkBtn = document.getElementById('add-bookmark-btn');
        this.addBookmarkBtnRight = document.getElementById('add-bookmark-btn-right');

        // Modals
        this.bookmarkModal = document.getElementById('bookmark-modal');
        this.addBookmarkModal = document.getElementById('add-bookmark-modal');
        this.bookmarkModalClose = document.getElementById('bookmark-modal-close');
        this.addBookmarkModalClose = document.getElementById('add-bookmark-modal-close');

        // Modal controls
        this.bookmarkSearch = document.getElementById('bookmark-search');
        this.bookmarkCategories = document.getElementById('bookmark-categories');
        this.addBookmarkForm = document.getElementById('add-bookmark-form');
    }

    bindEvents() {
        // Toggle sidebar events
        if (this.toggleLeft) {
            this.toggleLeft.addEventListener('click', () => this.openLeftSidebar());
        }

        if (this.toggleRight) {
            this.toggleRight.addEventListener('click', () => this.openRightSidebar());
        }

        // Close sidebar events
        if (this.closeLeft) {
            this.closeLeft.addEventListener('click', () => this.closeLeftSidebar());
        }

        if (this.closeRight) {
            this.closeRight.addEventListener('click', () => this.closeRightSidebar());
        }

        // Overlay click
        if (this.sidebarOverlay) {
            this.sidebarOverlay.addEventListener('click', () => {
                this.closeLeftSidebar();
                this.closeRightSidebar();
            });
        }

        // Manage bookmarks
        if (this.manageBookmarksBtn) {
            this.manageBookmarksBtn.addEventListener('click', () => this.openManageModal());
        }

        if (this.manageBookmarksRightBtn) {
            this.manageBookmarksRightBtn.addEventListener('click', () => this.openManageModal());
        }

        // Add bookmark
        if (this.addBookmarkBtn) {
            this.addBookmarkBtn.addEventListener('click', () => this.openAddBookmarkModal());
        }

        if (this.addBookmarkBtnRight) {
            this.addBookmarkBtnRight.addEventListener('click', () => this.openAddBookmarkModal());
        }

        // Modal close events
        if (this.bookmarkModalClose) {
            this.bookmarkModalClose.addEventListener('click', () => this.closeManageModal());
        }

        if (this.addBookmarkModalClose) {
            this.addBookmarkModalClose.addEventListener('click', () => this.closeAddBookmarkModal());
        }

        // Form submission
        if (this.addBookmarkForm) {
            this.addBookmarkForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAddBookmark();
            });
        }

        // Keyboard events
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeLeftSidebar();
                this.closeRightSidebar();
                this.closeManageModal();
                this.closeAddBookmarkModal();
            }
        });
    }

    // Sidebar control methods
    openLeftSidebar() {
        this.leftSidebarOpen = true;
        if (this.sidebarLeft) {
            this.sidebarLeft.classList.add('open');
        }
        this.updateOverlay();
    }

    closeLeftSidebar() {
        this.leftSidebarOpen = false;
        if (this.sidebarLeft) {
            this.sidebarLeft.classList.remove('open');
        }
        this.updateOverlay();
    }

    openRightSidebar() {
        this.rightSidebarOpen = true;
        if (this.sidebarRight) {
            this.sidebarRight.classList.add('open');
        }
        this.updateOverlay();
    }

    closeRightSidebar() {
        this.rightSidebarOpen = false;
        if (this.sidebarRight) {
            this.sidebarRight.classList.remove('open');
        }
        this.updateOverlay();
    }

    updateOverlay() {
        if (this.sidebarOverlay) {
            if (this.leftSidebarOpen || this.rightSidebarOpen) {
                this.sidebarOverlay.classList.add('active');
            } else {
                this.sidebarOverlay.classList.remove('active');
            }
        }
    }

    // Bookmark management
    populateSidebars() {
        this.populateBookmarkList();
        this.populateQuickAccess();
        this.populateCategories();
    }

    populateBookmarkList() {
        const frequentBookmarksLeft = this.getFrequentBookmarksLeft();
        const recentBookmarksRight = this.getRecentBookmarksRight();

        // Populate left sidebar
        if (this.bookmarkList) {
            this.bookmarkList.innerHTML = '';
            frequentBookmarksLeft.forEach(bookmark => {
                const element = this.createBookmarkElement(bookmark);
                this.bookmarkList.appendChild(element);
            });
        }

        // Populate right sidebar
        if (this.bookmarkListRight) {
            this.bookmarkListRight.innerHTML = '';
            recentBookmarksRight.forEach(bookmark => {
                const element = this.createBookmarkElement(bookmark);
                this.bookmarkListRight.appendChild(element);
            });
        }
    }

    populateQuickAccess() {
        if (!this.quickAccessList) return;

        const quickAccessItems = this.bookmarks
            .filter(bookmark => bookmark.category === 'favorites')
            .slice(0, 3);

        this.quickAccessList.innerHTML = '';
        quickAccessItems.forEach(bookmark => {
            const element = this.createQuickAccessElement(bookmark);
            this.quickAccessList.appendChild(element);
        });
    }

    populateCategories() {
        if (!this.categoryList) return;

        this.categoryList.innerHTML = '';
        this.categories.forEach(category => {
            const count = this.bookmarks.filter(b => b.category === category.id).length;
            const element = this.createCategoryElement(category, count);
            this.categoryList.appendChild(element);
        });
    }

    getFrequentBookmarksLeft() {
        return this.bookmarks
            .sort((a, b) => (b.usage || 0) - (a.usage || 0))
            .slice(0, 5);
    }

    getRecentBookmarksRight() {
        return this.bookmarks
            .filter(bookmark => bookmark.category === 'recent')
            .slice(0, 5);
    }

    createBookmarkElement(bookmark) {
        const element = document.createElement('button');
        element.className = 'bookmark-item';
        element.innerHTML = `
            <div class="bookmark-item-icon">
                <i class="${bookmark.icon || 'fas fa-bookmark'}"></i>
            </div>
            <div class="bookmark-item-content">
                <div class="bookmark-item-title">${bookmark.name}</div>
                <div class="bookmark-item-description">${bookmark.description || ''}</div>
            </div>
            <div class="bookmark-item-meta">
                <div class="bookmark-usage">${bookmark.usage || 0}</div>
                <button class="bookmark-remove" title="Remove bookmark">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Click handler for navigation
        element.addEventListener('click', (e) => {
            if (e.target.closest('.bookmark-remove')) {
                this.removeBookmark(bookmark.id);
                return;
            }

            this.trackUsage(bookmark.id);
            this.navigateToBookmark(bookmark);
        });

        return element;
    }

    createQuickAccessElement(bookmark) {
        const element = document.createElement('button');
        element.className = 'quick-access-item';
        element.innerHTML = `
            <div class="quick-access-item-icon">
                <i class="${bookmark.icon || 'fas fa-star'}"></i>
            </div>
            <div class="quick-access-item-content">
                <div class="quick-access-item-title">${bookmark.name}</div>
                <div class="quick-access-item-description">${bookmark.description || ''}</div>
            </div>
        `;

        element.addEventListener('click', () => {
            this.trackUsage(bookmark.id);
            this.navigateToBookmark(bookmark);
        });

        return element;
    }

    createCategoryElement(category, count) {
        const element = document.createElement('button');
        element.className = 'category-item';
        element.innerHTML = `
            <div class="category-item-icon">
                <i class="${category.icon || 'fas fa-folder'}"></i>
            </div>
            <div class="category-item-content">
                <div class="category-item-title">${category.name}</div>
                <div class="category-item-description">${count} items</div>
            </div>
        `;

        element.addEventListener('click', () => {
            this.filterByCategory(category.id);
        });

        return element;
    }

    // Bookmark operations
    addBookmark(bookmarkData) {
        const bookmark = {
            id: this.generateId(),
            name: bookmarkData.name,
            href: bookmarkData.url,
            icon: bookmarkData.icon || 'fas fa-bookmark',
            category: bookmarkData.category || 'favorites',
            description: bookmarkData.description || '',
            usage: 0,
            dateAdded: new Date().toISOString()
        };

        this.bookmarks.push(bookmark);
        this.saveBookmarks();
        this.populateSidebars();

        // Dispatch event
        this.dispatchEvent('bookmarkAdded', { bookmark });
    }

    removeBookmark(bookmarkId) {
        this.bookmarks = this.bookmarks.filter(b => b.id !== bookmarkId);
        this.saveBookmarks();
        this.populateSidebars();

        // Dispatch event
        this.dispatchEvent('bookmarkRemoved', { bookmarkId });
    }

    trackUsage(bookmarkId) {
        const bookmark = this.bookmarks.find(b => b.id === bookmarkId);
        if (bookmark) {
            bookmark.usage = (bookmark.usage || 0) + 1;
            bookmark.lastUsed = new Date().toISOString();
            this.saveBookmarks();
        }
    }

    navigateToBookmark(bookmark) {
        console.log('Navigating to bookmark:', bookmark);

        // Dispatch event for custom handling
        const event = new CustomEvent('bookmarkSelected', {
            detail: { bookmark }
        });
        document.dispatchEvent(event);

        // Default navigation
        if (bookmark.href) {
            window.location.href = bookmark.href;
        }

        // Close sidebars
        this.closeLeftSidebar();
        this.closeRightSidebar();
    }

    // Modal management
    openManageModal() {
        if (this.bookmarkModal) {
            this.bookmarkModal.classList.add('active');
        }
    }

    closeManageModal() {
        if (this.bookmarkModal) {
            this.bookmarkModal.classList.remove('active');
        }
    }

    openAddBookmarkModal() {
        if (this.addBookmarkModal) {
            this.addBookmarkModal.classList.add('active');
        }
    }

    closeAddBookmarkModal() {
        if (this.addBookmarkModal) {
            this.addBookmarkModal.classList.remove('active');
        }
        if (this.addBookmarkForm) {
            this.addBookmarkForm.reset();
        }
    }

    handleAddBookmark() {
        const formData = new FormData(this.addBookmarkForm);
        const bookmarkData = {
            name: formData.get('name'),
            url: formData.get('url'),
            icon: formData.get('icon'),
            category: formData.get('category'),
            description: formData.get('description')
        };

        this.addBookmark(bookmarkData);
        this.closeAddBookmarkModal();
    }

    // Storage methods
    loadBookmarks() {
        if (!this.options.autoSave) {
            return this.options.bookmarks;
        }

        try {
            const stored = localStorage.getItem(this.options.storageKey);
            return stored ? JSON.parse(stored) : this.options.bookmarks;
        } catch (e) {
            console.warn('Could not load bookmarks from storage:', e);
            return this.options.bookmarks;
        }
    }

    saveBookmarks() {
        if (!this.options.autoSave) return;

        try {
            localStorage.setItem(this.options.storageKey, JSON.stringify(this.bookmarks));
        } catch (e) {
            console.warn('Could not save bookmarks to storage:', e);
        }
    }

    // Utility methods
    generateId() {
        return 'bookmark_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    filterByCategory(categoryId) {
        console.log('Filtering by category:', categoryId);
        // Implement category filtering logic
    }

    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }

    // Public API methods
    toggleLeft() {
        if (this.leftSidebarOpen) {
            this.closeLeftSidebar();
        } else {
            this.openLeftSidebar();
        }
    }

    toggleRight() {
        if (this.rightSidebarOpen) {
            this.closeRightSidebar();
        } else {
            this.openRightSidebar();
        }
    }

    updateBookmarks(newBookmarks) {
        this.bookmarks = newBookmarks;
        this.saveBookmarks();
        this.populateSidebars();
    }

    getBookmarks() {
        return [...this.bookmarks];
    }
}

// Initialize the component when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.sidebarComponent = new SidebarComponent();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SidebarComponent;
}

// User credentials and branch permissions system
const USER_CREDENTIALS = {
    'admin': {
        password: 'admin123',
        role: 'admin',
        branches: ['main', 'north', 'south', 'east', 'west'], // Ad<PERSON> has access to all branches
        name: 'Administrator'
    },
    'user1': {
        password: 'user123',
        role: 'user',
        branches: ['main', 'north'], // Limited access
        name: '<PERSON>'
    },
    'user2': {
        password: 'user456',
        role: 'user',
        branches: ['south', 'east'], // Different limited access
        name: '<PERSON>'
    },
    'manager': {
        password: 'manager789',
        role: 'manager',
        branches: ['main', 'north', 'south'], // Manager level access
        name: '<PERSON>'
    }
};

// All available branches in the system
const ALL_BRANCHES = {
    'main': 'Main Branch',
    'north': 'North Branch',
    'south': 'South Branch',
    'east': 'East Branch',
    'west': 'West Branch'
};

// Current user session
let currentUser = null;

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeLoginPage();
    loadRememberedCredentials();
});

// Initialize login page functionality
function initializeLoginPage() {
    const loginForm = document.getElementById('loginForm');
    const forgotPasswordLink = document.getElementById('forgotPasswordLink');

    // Initialize form validation
    initializeFormValidation();

    // Initialize password toggle
    initializePasswordToggle();

    // Initialize form submission
    loginForm.addEventListener('submit', handleFormSubmission);

    // Initialize forgot password link
    forgotPasswordLink.addEventListener('click', handleForgotPassword);

    // Initialize input animations
    initializeInputAnimations();

    // Initialize dynamic branch loading
    initializeDynamicBranches();

    // Initialize remember me functionality
    initializeRememberMe();
}

// Form validation initialization
function initializeFormValidation() {
    const inputs = document.querySelectorAll('.mui-input, .mui-select-input');

    inputs.forEach(input => {
        input.addEventListener('blur', () => validateField(input));
        input.addEventListener('input', () => clearFieldError(input));
    });
}

// Password toggle functionality
function initializePasswordToggle() {
    const passwordInput = document.getElementById('password');
    const passwordToggle = document.getElementById('passwordToggle');
    const toggleIcon = passwordToggle.querySelector('.material-icons');

    passwordToggle.addEventListener('click', function() {
        const isPassword = passwordInput.type === 'password';

        passwordInput.type = isPassword ? 'text' : 'password';
        toggleIcon.textContent = isPassword ? 'visibility' : 'visibility_off';

        // Update ARIA label
        passwordToggle.setAttribute('aria-label',
            isPassword ? 'Hide password' : 'Show password'
        );
    });
}

// Input animations for Material-UI style
function initializeInputAnimations() {
    const inputs = document.querySelectorAll('.mui-input');

    inputs.forEach(input => {
        // Check if input has value on page load
        if (input.value.trim() !== '') {
            input.classList.add('has-value');
        }

        input.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
    });

    // Handle select field
    const selectInput = document.getElementById('branch');
    selectInput.addEventListener('change', function() {
        if (this.value !== '') {
            this.classList.add('has-value');
        } else {
            this.classList.remove('has-value');
        }
    });
}

// Initialize dynamic branch loading based on user credentials
function initializeDynamicBranches() {
    const loginIdInput = document.getElementById('loginId');
    const passwordInput = document.getElementById('password');

    // Initialize the searchable branch dropdown
    initializeSearchableBranchDropdown();

    // Listen for changes in login ID to dynamically load branches
    loginIdInput.addEventListener('input', function() {
        updateBranchOptions();
    });

    passwordInput.addEventListener('input', function() {
        updateBranchOptions();
    });
}

// Initialize searchable branch dropdown
function initializeSearchableBranchDropdown() {
    const branchSearch = document.getElementById('branchSearch');
    const branchDropdown = document.getElementById('branchDropdown');
    const branchOptions = document.getElementById('branchOptions');
    const branchHidden = document.getElementById('branch');

    // Initially disable the branch search
    branchSearch.disabled = true;

    // Handle search input focus
    branchSearch.addEventListener('focus', function() {
        if (!this.disabled) {
            branchDropdown.classList.add('open');
            // Add active class to wrapper for label animation
            document.querySelector('.mui-select-wrapper').classList.add('active');
        }
    });

    // Handle search input
    branchSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const wrapper = document.querySelector('.mui-select-wrapper');

        // Update label animation based on input value
        if (this.value.trim() !== '') {
            wrapper.classList.add('active');
            this.classList.add('has-value');
        } else {
            // Only remove active if not focused and no value
            if (document.activeElement !== this) {
                wrapper.classList.remove('active');
            }
            this.classList.remove('has-value');
        }

        filterBranchOptions(searchTerm);
    });

    // Handle blur event to maintain label state
    branchSearch.addEventListener('blur', function() {
        const wrapper = document.querySelector('.mui-select-wrapper');
        // Keep label up only if there's a value
        if (this.value.trim() !== '') {
            wrapper.classList.add('active');
            this.classList.add('has-value');
        } else {
            wrapper.classList.remove('active');
            this.classList.remove('has-value');
        }
    });

    // Handle clicking outside to close dropdown
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.mui-select-wrapper')) {
            branchDropdown.classList.remove('open');
        }
    });

    // Handle option selection
    branchOptions.addEventListener('click', function(event) {
        if (event.target.classList.contains('mui-select-option') && !event.target.classList.contains('disabled') && !event.target.classList.contains('no-results')) {
            const selectedValue = event.target.dataset.value;
            const selectedText = event.target.textContent;
            const wrapper = document.querySelector('.mui-select-wrapper');

            branchSearch.value = selectedText;
            branchHidden.value = selectedValue;
            branchSearch.classList.add('has-value');
            wrapper.classList.add('active');
            branchDropdown.classList.remove('open');

            // Update selected state
            document.querySelectorAll('.mui-select-option').forEach(opt => opt.classList.remove('selected'));
            event.target.classList.add('selected');

            // Clear any errors
            clearFieldError(branchHidden);
        }
    });
}

// Filter branch options based on search term
function filterBranchOptions(searchTerm) {
    const options = document.querySelectorAll('.mui-select-option:not(.no-results)');
    let visibleCount = 0;

    options.forEach(option => {
        const text = option.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            option.style.display = 'block';
            visibleCount++;
        } else {
            option.style.display = 'none';
        }
    });

    // Show/hide no results message
    let noResultsOption = document.querySelector('.mui-select-option.no-results');
    if (visibleCount === 0 && searchTerm.length > 0) {
        if (!noResultsOption) {
            noResultsOption = document.createElement('div');
            noResultsOption.className = 'mui-select-option no-results';
            noResultsOption.textContent = 'No branches found';
            document.getElementById('branchOptions').appendChild(noResultsOption);
        }
        noResultsOption.style.display = 'block';
    } else if (noResultsOption) {
        noResultsOption.style.display = 'none';
    }
}

// Update branch options based on current user credentials
function updateBranchOptions() {
    const loginId = document.getElementById('loginId').value.trim();
    const password = document.getElementById('password').value.trim();
    const branchSearch = document.getElementById('branchSearch');
    const branchOptions = document.getElementById('branchOptions');
    const branchHidden = document.getElementById('branch');
    const credentialStatus = document.getElementById('credentialStatus');

    // Clear current options
    branchOptions.innerHTML = '';

    // If both login ID and password are provided, check credentials
    if (loginId && password) {
        const user = USER_CREDENTIALS[loginId];

        if (user && user.password === password) {
            // Valid credentials - enable branch dropdown and populate options
            currentUser = { ...user, loginId };
            branchSearch.disabled = false;
            branchSearch.classList.remove('has-value');

            // Populate branches based on user permissions
            user.branches.forEach(branchKey => {
                const option = document.createElement('div');
                option.className = 'mui-select-option';
                option.dataset.value = branchKey;
                option.textContent = ALL_BRANCHES[branchKey];
                branchOptions.appendChild(option);
            });

            // Auto-select Main Branch for admin users
            if (user.role === 'admin') {
                const mainBranchOption = branchOptions.querySelector('[data-value="main"]');
                const wrapper = document.querySelector('.mui-select-wrapper');
                if (mainBranchOption) {
                    branchSearch.value = 'Main Branch';
                    branchHidden.value = 'main';
                    branchSearch.classList.add('has-value');
                    wrapper.classList.add('active');
                    mainBranchOption.classList.add('selected');
                }
            } else {
                // Clear selection for non-admin users
                const wrapper = document.querySelector('.mui-select-wrapper');
                branchSearch.value = '';
                branchHidden.value = '';
                branchSearch.classList.remove('has-value');
                wrapper.classList.remove('active');
            }

            // Show success status
            credentialStatus.textContent = `✓ Welcome ${user.name} (${user.role}) - ${user.branches.length} branch(es) available`;
            credentialStatus.className = 'credential-status valid';

            // Add visual feedback for successful credential validation
            document.getElementById('loginId').classList.remove('error');
            document.getElementById('password').classList.remove('error');

        } else {
            // Invalid credentials - keep branch dropdown disabled
            currentUser = null;
            const wrapper = document.querySelector('.mui-select-wrapper');
            branchSearch.disabled = true;
            branchSearch.value = '';
            branchHidden.value = '';
            branchSearch.classList.remove('has-value');
            wrapper.classList.remove('active');

            // Show error status
            credentialStatus.textContent = '⚠ Invalid credentials - please check your login ID and password';
            credentialStatus.className = 'credential-status invalid';
        }
    } else {
        // Incomplete credentials - disable branch dropdown
        currentUser = null;
        const wrapper = document.querySelector('.mui-select-wrapper');
        branchSearch.disabled = true;
        branchSearch.value = '';
        branchHidden.value = '';
        branchSearch.classList.remove('has-value');
        wrapper.classList.remove('active');

        // Clear status
        credentialStatus.textContent = '';
        credentialStatus.className = 'credential-status';
    }
}

// Field validation with credential checking
function validateField(field) {
    const fieldName = field.name;
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';

    switch (fieldName) {
        case 'loginId':
            if (!value) {
                isValid = false;
                errorMessage = 'Login ID is required';
            } else if (value.length < 3) {
                isValid = false;
                errorMessage = 'Login ID must be at least 3 characters';
            } else if (!USER_CREDENTIALS[value]) {
                isValid = false;
                errorMessage = 'Invalid login ID';
            }
            break;

        case 'password':
            if (!value) {
                isValid = false;
                errorMessage = 'Password is required';
            } else if (value.length < 6) {
                isValid = false;
                errorMessage = 'Password must be at least 6 characters';
            } else {
                // Check password against credentials if login ID is provided
                const loginId = document.getElementById('loginId').value.trim();
                if (loginId && USER_CREDENTIALS[loginId]) {
                    if (USER_CREDENTIALS[loginId].password !== value) {
                        isValid = false;
                        errorMessage = 'Invalid password';
                    }
                }
            }
            break;

        case 'branch':
            // Get the hidden branch value
            const branchValue = document.getElementById('branch').value;
            if (!branchValue) {
                isValid = false;
                errorMessage = 'Please select a branch';
            } else if (!currentUser) {
                isValid = false;
                errorMessage = 'Please enter valid credentials first';
            } else if (!currentUser.branches.includes(branchValue)) {
                isValid = false;
                errorMessage = 'You do not have access to this branch';
            }
            break;
    }

    displayFieldError(fieldName, isValid ? '' : errorMessage);
    return isValid;
}

// Display field error
function displayFieldError(fieldName, errorMessage) {
    const errorElement = document.getElementById(fieldName + 'Error');
    if (errorElement) {
        errorElement.textContent = errorMessage;

        // Add error styling to field
        const field = document.getElementById(fieldName);
        if (errorMessage) {
            field.classList.add('error');
        } else {
            field.classList.remove('error');
        }
    }
}

// Clear field error
function clearFieldError(field) {
    const fieldName = field.name;
    const errorElement = document.getElementById(fieldName + 'Error');
    if (errorElement && errorElement.textContent) {
        errorElement.textContent = '';
        field.classList.remove('error');
    }
}

// Form submission handler with enhanced credential validation
function handleFormSubmission(event) {
    event.preventDefault();

    const loginId = document.getElementById('loginId');
    const password = document.getElementById('password');
    const branch = document.getElementById('branch');
    const branchSearch = document.getElementById('branchSearch');
    const loginButton = document.getElementById('loginButton');
    const buttonText = loginButton.querySelector('.button-text');
    const buttonLoader = document.getElementById('buttonLoader');

    // Validate all fields
    const isLoginIdValid = validateField(loginId);
    const isPasswordValid = validateField(password);
    const isBranchValid = validateField(branch);

    const isFormValid = isLoginIdValid && isPasswordValid && isBranchValid;

    if (!isFormValid) {
        // Focus on first invalid field
        const firstInvalidField = document.querySelector('.error');
        if (firstInvalidField) {
            firstInvalidField.focus();
        }
        return;
    }

    // Additional credential validation
    const user = USER_CREDENTIALS[loginId.value.trim()];
    if (!user || user.password !== password.value.trim()) {
        displayFieldError('loginId', 'Invalid credentials');
        displayFieldError('password', 'Invalid credentials');
        loginId.focus();
        return;
    }

    // Check branch access
    if (!user.branches.includes(branch.value)) {
        displayFieldError('branch', 'Access denied to this branch');
        branch.focus();
        return;
    }

    // Show loading state
    loginButton.disabled = true;
    buttonText.style.opacity = '0';
    buttonLoader.style.display = 'block';

    // Handle Remember Me functionality
    const rememberMe = document.getElementById('rememberMe');
    if (rememberMe.checked) {
        saveCredentials(loginId.value, password.value);
    } else {
        clearSavedCredentials();
    }

    // Simulate login process
    setTimeout(() => {
        // Hide loading state
        loginButton.disabled = false;
        buttonText.style.opacity = '1';
        buttonLoader.style.display = 'none';

        // Check if user is admin and redirect to dashboard
        if (user.role === 'admin') {
            redirectToDashboard({
                loginId: loginId.value,
                name: user.name,
                role: user.role,
                branch: branchSearch.value || ALL_BRANCHES[branch.value],
                accessibleBranches: user.branches.length
            });
        } else {
            // Show success message for non-admin users
            showLoginResult({
                loginId: loginId.value,
                name: user.name,
                role: user.role,
                branch: branchSearch.value || ALL_BRANCHES[branch.value],
                accessibleBranches: user.branches.length
            });
        }
    }, 2000);
}

// Redirect to dashboard for admin users
function redirectToDashboard(userData) {
    const roleDisplay = userData.role.charAt(0).toUpperCase() + userData.role.slice(1);

    // Store user data for dashboard
    localStorage.setItem('dashboard_user_name', userData.name);
    localStorage.setItem('dashboard_user_role', roleDisplay);
    localStorage.setItem('dashboard_login_id', userData.loginId);
    localStorage.setItem('dashboard_branch', userData.branch);

    // Redirect to dashboard with user parameters
    const dashboardUrl = `dashboard.html?user=${encodeURIComponent(userData.name)}&role=${encodeURIComponent(roleDisplay)}&branch=${encodeURIComponent(userData.branch)}`;
    window.location.href = dashboardUrl;
}

// Show enhanced login result with user details (for non-admin users)
function showLoginResult(userData) {
    const roleDisplay = userData.role.charAt(0).toUpperCase() + userData.role.slice(1);
    const message = `🎉 Login Successful!\n\n` +
                   `👤 User: ${userData.name} (${userData.loginId})\n` +
                   `🏢 Role: ${roleDisplay}\n` +
                   `🏪 Selected Branch: ${userData.branch}\n` +
                   `🔑 Total Branch Access: ${userData.accessibleBranches} branch(es)\n\n` +
                   `Welcome to the Prevost system!`;
    alert(message);
}

// Forgot password handler
function handleForgotPassword(event) {
    event.preventDefault();

    const email = prompt('Please enter your email address for password reset:');

    if (email && isValidEmail(email)) {
        alert(`Password reset instructions have been sent to ${email}`);
    } else if (email) {
        alert('Please enter a valid email address');
    }
}

// Email validation helper
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Keyboard navigation enhancement
document.addEventListener('keydown', function(event) {
    // Enter key on form fields should move to next field or submit
    if (event.key === 'Enter') {
        const activeElement = document.activeElement;
        const formFields = Array.from(document.querySelectorAll('.mui-input, .mui-select-search'));
        const currentIndex = formFields.indexOf(activeElement);

        if (currentIndex >= 0 && currentIndex < formFields.length - 1) {
            event.preventDefault();
            formFields[currentIndex + 1].focus();
        }
    }
});

// Add error styles to CSS dynamically
const style = document.createElement('style');
style.textContent = `
    .mui-input.error,
    .mui-select-search.error {
        border-bottom-color: #d32f2f !important;
    }

    .mui-input.error + .mui-label,
    .mui-select-search.error + .mui-select-label {
        color: #d32f2f !important;
    }

    .mui-input.error ~ .mui-underline {
        background: #d32f2f !important;
    }
`;
document.head.appendChild(style);

// Accessibility enhancements
function enhanceAccessibility() {
    // Add ARIA live region for form errors
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only';
    liveRegion.style.position = 'absolute';
    liveRegion.style.left = '-10000px';
    liveRegion.style.width = '1px';
    liveRegion.style.height = '1px';
    liveRegion.style.overflow = 'hidden';
    document.body.appendChild(liveRegion);

    // Announce form errors to screen readers
    const originalDisplayFieldError = displayFieldError;
    displayFieldError = function(fieldName, errorMessage) {
        originalDisplayFieldError(fieldName, errorMessage);
        if (errorMessage) {
            liveRegion.textContent = `Error in ${fieldName}: ${errorMessage}`;
        }
    };
}

// Initialize accessibility enhancements
enhanceAccessibility();

// Performance optimization: Debounce input validation
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply debounced validation to inputs
document.addEventListener('DOMContentLoaded', function() {
    const debouncedValidation = debounce(validateField, 300);
    const inputs = document.querySelectorAll('.mui-input, .mui-select-search');

    inputs.forEach(input => {
        input.addEventListener('input', () => debouncedValidation(input));
    });
});

// Remember Me functionality
function initializeRememberMe() {
    const rememberMeCheckbox = document.getElementById('rememberMe');
    const loginIdInput = document.getElementById('loginId');
    const passwordInput = document.getElementById('password');

    // Check if credentials are saved and auto-fill if Remember Me was checked
    const savedCredentials = getSavedCredentials();
    if (savedCredentials) {
        loginIdInput.value = savedCredentials.loginId;
        passwordInput.value = savedCredentials.password;
        rememberMeCheckbox.checked = true;

        // Trigger input events to update UI state
        loginIdInput.dispatchEvent(new Event('input'));
        passwordInput.dispatchEvent(new Event('input'));

        // Add has-value class for proper label positioning
        loginIdInput.classList.add('has-value');
        passwordInput.classList.add('has-value');

        // Update branch options since credentials are pre-filled
        updateBranchOptions();
    }
}

function saveCredentials(loginId, password) {
    try {
        const credentials = {
            loginId: loginId,
            password: password,
            timestamp: Date.now()
        };
        localStorage.setItem('prevost_remembered_credentials', JSON.stringify(credentials));
    } catch (error) {
        console.warn('Could not save credentials to localStorage:', error);
    }
}

function getSavedCredentials() {
    try {
        const saved = localStorage.getItem('prevost_remembered_credentials');
        if (saved) {
            const credentials = JSON.parse(saved);
            // Check if credentials are not older than 30 days
            const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000;
            if (Date.now() - credentials.timestamp < thirtyDaysInMs) {
                return credentials;
            } else {
                // Remove expired credentials
                clearSavedCredentials();
            }
        }
    } catch (error) {
        console.warn('Could not retrieve credentials from localStorage:', error);
    }
    return null;
}

function clearSavedCredentials() {
    try {
        localStorage.removeItem('prevost_remembered_credentials');
    } catch (error) {
        console.warn('Could not clear credentials from localStorage:', error);
    }
}

function loadRememberedCredentials() {
    // This function is called on page load to check for saved credentials
    const savedCredentials = getSavedCredentials();
    if (savedCredentials) {
        // Credentials will be loaded in initializeRememberMe()
        return true;
    }
    return false;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cambria', serif;
    background-color: #000000;
    color: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Main container */
.login-container {
    min-height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
    background-image: url('MainBackground-Prevost.png');
    background-size: cover;
    background-position: center 20%;
    background-repeat: no-repeat;
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    padding: 2rem 1rem;
}

/* Logo section - now inside card */
.logo-section {
    margin-bottom: 0.5rem;
    text-align: center;
}

.logo {
    max-width: 120px;
    height: auto;
}

/* Login form container */
.login-form-container {
    width: 100%;
    max-width: 400px;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.login-title {
    font-family: 'Cambria', serif;
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c2c2c;
    text-align: center;
    margin-bottom: 0.2rem;
}

.login-subtitle {
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #666666;
    text-align: center;
    margin-bottom: 1rem;
}

/* Form styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.form-field {
    position: relative;
}

/* Material-UI inspired text field */
.mui-textfield {
    position: relative;
    display: flex;
    align-items: center;
}

.mui-input {
    width: 100%;
    padding: 0.8rem 0 0.4rem 0;
    border: none;
    outline: none;
    background: transparent;
    font-family: 'Cambria', serif;
    font-size: 1rem;
    color: #2c2c2c;
    border-bottom: 1px solid #cccccc;
    transition: border-color 0.3s ease;
}

.mui-input:focus {
    border-bottom-color: #2c2c2c;
}

.mui-input:focus + .mui-label,
.mui-input:not(:placeholder-shown) + .mui-label,
.mui-input:valid + .mui-label {
    transform: translateY(-1.2rem) scale(0.75);
    color: #2c2c2c;
}

.mui-label {
    position: absolute;
    left: 0;
    top: 0.8rem;
    font-family: 'Cambria', serif;
    font-size: 1rem;
    color: #666666;
    pointer-events: none;
    transition: all 0.3s ease;
    transform-origin: left top;
}

.mui-underline {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: #2c2c2c;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.mui-input:focus ~ .mui-underline {
    transform: scaleX(1);
}

/* Password field specific styles */
.password-field {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #666666;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.password-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #2c2c2c;
}

/* Enhanced Select field styles with search */
.mui-select-container {
    position: relative;
}

.mui-select-wrapper {
    position: relative;
}

.mui-select-search {
    width: 100%;
    padding: 0.6rem 2rem 0.3rem 0;
    border: none;
    outline: none;
    background: transparent;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #2c2c2c;
    border-bottom: 1px solid #cccccc;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mui-select-search:focus {
    border-bottom-color: #2c2c2c;
    cursor: text;
}

.mui-select-search:disabled {
    color: #999999;
    background-color: #f5f5f5;
    border-bottom-color: #e0e0e0;
    cursor: not-allowed;
}

.mui-select-wrapper.active .mui-select-label,
.mui-select-search:focus + .mui-select-label,
.mui-select-search:not(:placeholder-shown) + .mui-select-label,
.mui-select-search.has-value + .mui-select-label,
.mui-select-search[value]:not([value=""]) + .mui-select-label {
    transform: translateY(-1.2rem) scale(0.75);
    color: #2c2c2c;
}

.mui-select-search:disabled + .mui-select-label {
    color: #999999;
}

.mui-select-label {
    position: absolute;
    left: 0;
    top: 0.6rem;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #666666;
    pointer-events: none;
    transition: all 0.3s ease;
    transform-origin: left top;
    z-index: 1;
}

.mui-select-arrow {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #666666;
    pointer-events: none;
    transition: all 0.3s ease;
}

.mui-select-search:disabled ~ .mui-select-arrow {
    color: #cccccc;
}

.mui-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #ffffff;
    border: 1px solid #cccccc;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.mui-select-dropdown.open {
    display: block;
}

.mui-select-options {
    padding: 0;
}

.mui-select-option {
    padding: 0.75rem 1rem;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #2c2c2c;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border-bottom: 1px solid #f0f0f0;
}

.mui-select-option:last-child {
    border-bottom: none;
}

.mui-select-option:hover {
    background-color: #f8f9fa;
}

.mui-select-option.selected {
    background-color: #2c2c2c;
    color: #ffffff;
}

.mui-select-option.disabled {
    color: #cccccc;
    cursor: not-allowed;
    background-color: #f9f9f9;
}

.mui-select-option.no-results {
    color: #666666;
    font-style: italic;
    cursor: default;
    text-align: center;
}

.mui-select-option.no-results:hover {
    background-color: transparent;
}

/* Credential status indicator */
.credential-status {
    font-family: 'Cambria', serif;
    font-size: 0.8rem;
    margin-top: 0.2rem;
    min-height: 1rem;
    transition: all 0.3s ease;
}

.credential-status.valid {
    color: #2e7d32;
}

.credential-status.invalid {
    color: #f57c00;
}

/* Error messages */
.error-message {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #d32f2f;
    margin-top: 0.25rem;
    min-height: 1.25rem;
}

/* Remember Me checkbox styles */
.remember-me-container {
    display: flex;
    align-items: center;
    margin: 0.5rem 0;
}

.remember-me-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #2c2c2c;
    user-select: none;
}

.remember-me-checkbox {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.remember-me-checkmark {
    position: relative;
    height: 18px;
    width: 18px;
    background-color: transparent;
    border: 2px solid #cccccc;
    border-radius: 3px;
    margin-right: 0.5rem;
    transition: all 0.3s ease;
}

.remember-me-label:hover .remember-me-checkmark {
    border-color: #2c2c2c;
}

.remember-me-checkbox:checked + .remember-me-checkmark {
    background-color: #2c2c2c;
    border-color: #2c2c2c;
}

.remember-me-checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.remember-me-checkbox:checked + .remember-me-checkmark:after {
    display: block;
}

.remember-me-text {
    transition: color 0.3s ease;
}

.remember-me-label:hover .remember-me-text {
    color: #666666;
}

/* Login button */
.login-button {
    width: 100%;
    padding: 0.7rem;
    background: #2c2c2c;
    color: #ffffff;
    border: none;
    border-radius: 6px;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-top: 0.5rem;
}

.login-button:hover {
    background: #404040;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.login-button:active {
    transform: translateY(0);
}

.login-button:disabled {
    background: #cccccc;
    color: #666666;
    cursor: not-allowed;
    transform: none;
}

.button-text {
    display: inline-block;
    transition: opacity 0.3s ease;
}

.button-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Demo credentials section */
.demo-credentials-section {
    margin-top: 0.8rem;
    text-align: center;
}

.demo-credentials {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 0.8rem;
    margin-bottom: 0.8rem;
}

.demo-credentials-toggle {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #2c2c2c;
    cursor: pointer;
    list-style: none;
    outline: none;
    transition: color 0.3s ease;
}

.demo-credentials-toggle:hover {
    color: #666666;
}

.demo-credentials-toggle::-webkit-details-marker {
    display: none;
}

.demo-credentials-toggle::before {
    content: '▶ ';
    transition: transform 0.3s ease;
}

.demo-credentials[open] .demo-credentials-toggle::before {
    transform: rotate(90deg);
}

.demo-credentials-content {
    margin-top: 0.8rem;
    text-align: left;
}

.credential-item {
    font-family: 'Cambria', serif;
    font-size: 0.8rem;
    color: #666666;
    margin-bottom: 0.6rem;
    padding: 0.4rem;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    border-left: 3px solid #2c2c2c;
}

.credential-item strong {
    color: #2c2c2c;
}

.credential-item small {
    color: #888888;
    font-style: italic;
}

/* Forgot password link */
.forgot-password-section {
    text-align: center;
    margin-top: 0.5rem;
}

.forgot-password-link {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #2c2c2c;
    text-decoration: none;
    transition: color 0.3s ease;
}

.forgot-password-link:hover {
    color: #666666;
    text-decoration: underline;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.9);
    padding: 1rem;
    position: relative;
    z-index: 2;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-left,
.footer-center,
.footer-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: 'Cambria', serif;
    font-size: 0.8rem;
    color: #cccccc;
}

.footer-link {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #ffffff;
}

.footer-separator {
    color: #666666;
    margin: 0 0.25rem;
}

.version-text,
.date-text {
    color: #999999;
    font-weight: 500;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.contact-item .material-icons {
    font-size: 14px;
    color: #999999;
}

.contact-text {
    font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
    .content-wrapper {
        padding: 1rem;
    }

    .login-card {
        padding: 0.8rem;
    }

    .logo {
        max-width: 100px;
    }

    .login-title {
        font-size: 1.2rem;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .footer-left,
    .footer-center,
    .footer-right {
        flex-wrap: wrap;
        justify-content: center;
    }

    .footer-separator {
        display: none;
    }
}

@media (max-width: 480px) {
    .login-card {
        padding: 0.7rem;
        margin: 0 0.5rem;
    }

    .logo {
        max-width: 90px;
    }

    .demo-credentials {
        padding: 0.5rem;
    }

    .credential-item {
        font-size: 0.75rem;
        padding: 0.3rem;
        margin-bottom: 0.3rem;
    }

    .remember-me-label {
        font-size: 0.8rem;
    }

    .remember-me-checkmark {
        height: 16px;
        width: 16px;
    }
}
